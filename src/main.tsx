import { createRoot } from 'react-dom/client'
import App from './App.tsx'
import './index.css'
import 'antd/dist/reset.css';
import ReactGA from "react-ga4";
import { EnvKeys } from './config/constants.ts';

if (EnvKeys.googleAnalyticsMeasurementId) {
  ReactGA.initialize(EnvKeys.googleAnalyticsMeasurementId);
} else {
  console.warn("Google Analytics Measurement ID is not defined. GA will not be initialized.");
}

createRoot(document.getElementById("root")!).render(<App />);
