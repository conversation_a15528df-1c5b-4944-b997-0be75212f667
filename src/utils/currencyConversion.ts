// Add currency conversion rates (you might want to fetch these from an API in production)
const CURRENCY_RATES = {
  USD: 1,
  INR: 0.011723,
  EURO: 1.123973, // 1 EUR = 1.123973 USD
  YUAN: 0.138767, // 1 CNY = 0.14 USD
  YEN: 0.00685, // 1 JPY = 0.00685 USD
  AED: 0.272259, // 1 AED = 0.272259 USD
};

// Helper function to convert currency to USD
export const convertToUSD = (amount: number, fromCurrency: string): number => {
  const rate = CURRENCY_RATES[fromCurrency as keyof typeof CURRENCY_RATES] || 1;
  return amount * rate;
};
