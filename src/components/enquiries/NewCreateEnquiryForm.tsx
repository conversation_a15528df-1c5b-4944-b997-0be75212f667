import { useState, useRef, useEffect, useCallback } from "react";
import { useEnquiryForm } from "./hooks/useEnquiryForm";
import { toast } from "sonner";
import { Plus, ChevronDown, ChevronUp } from "lucide-react";
import { Select as AntSelect, AutoComplete } from "antd";
import { supabase } from "@/integrations/supabase/client";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { useLocalStorage } from "@/hooks/useLocalStorage";
import { SALES_TEAM_MAPPING, CATEGORIES } from "@/config/salesTeamMapping";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import axios from "axios";
import { useDebounce } from "@/hooks/useDebounce";
import { API_CONFIG } from '@/config/api';
import { citiesByCountry, portsByCountry, countryFlags } from "@/utils/destinations";
import { Upload } from "lucide-react";

// Define interface for customer data
interface CustomerDetails {
  id: string;
  customer_full_name: string;
  customer_poc?: string;
  customer_phone?: string;
  customer_email?: string;
  city?: string;
  country?: string;
  industries?: string[];
}

// We're now using AntSelect for all dropdowns
import CompactDocumentUpload from "./components/CompactDocumentUpload";
import EnquirySuccessDialog from "./components/EnquirySuccessDialog";
import "./styles/enquiry-form.css";
import DocumentUploadModal from "../DocumentUploadModal";

const NewCreateEnquiryForm = () => {
  // Add this at the top of your component, before the return statement
  useEffect(() => {
    // Create a style element
    const styleElement = document.createElement('style');
    styleElement.innerHTML = `
      /* Make all input placeholders match AutoComplete style */
      .enquiry-form input::placeholder,
      .enquiry-form textarea::placeholder,
      .enquiry-form select::placeholder {
        color: rgba(0, 0, 0, 0.2) !important;
        opacity: 1 !important;
        font-size: 0.875rem !important;
        font-weight: 400 !important;
      }
      
      /* For Microsoft Edge */
      .enquiry-form input::-ms-input-placeholder,
      .enquiry-form textarea::-ms-input-placeholder,
      .enquiry-form select::-ms-input-placeholder {
        color: rgba(0, 0, 0, 0.2) !important;
        opacity: 1 !important;
        font-size: 0.875rem !important;
        font-weight: 400 !important;
      }
      
      /* For Firefox */
      .enquiry-form input::-moz-placeholder,
      .enquiry-form textarea::-moz-placeholder,
      .enquiry-form select::-moz-placeholder {
        color: rgba(0, 0, 0, 0.2) !important;
        opacity: 1 !important;
        font-size: 0.875rem !important;
        font-weight: 400 !important;
      }
      
      /* For Chrome, Safari, and Opera */
      .enquiry-form input::-webkit-input-placeholder,
      .enquiry-form textarea::-webkit-input-placeholder,
      .enquiry-form select::-webkit-input-placeholder {
        color: rgba(0, 0, 0, 0.2) !important;
        opacity: 1 !important;
        font-size: 0.875rem !important;
        font-weight: 400 !important;
      }
      
      /* Ensure consistent padding */
      .enquiry-form input,
      .enquiry-form textarea,
      .enquiry-form select {
        padding-left: 0.75rem !important;
        font-size: 0.875rem !important;
      }
    `;
    
    // Add an ID to identify this style element
    styleElement.id = 'placeholder-styles';
    
    // Append the style element to the document head
    document.head.appendChild(styleElement);
    
    // Clean up function to remove the style element when component unmounts
    return () => {
      const existingStyle = document.getElementById('placeholder-styles');
      if (existingStyle) {
        existingStyle.remove();
      }
    };
  }, []);


  const userCountry = localStorage.getItem("userCountry") || "USA";
  const userCategory = localStorage.getItem("userCategory");
  const userRole = localStorage.getItem("userRole");
  const [session, setSession] = useState<any>(null);

  // Add validation for user category
  const hasUserCategory = userCategory && userCategory.trim() !== "";
  const [showDocModal, setShowDocModal] = useState(false);
  const [filesByType, setFilesByType] = useState({
    tds: [],
    sds: [],
    coa: [],
    moa: [],
    others: [],
  });

const [currentChemicalIndex, setCurrentChemicalIndex] = useState(0); // Add this state
const handleFilesChange = (type, files) => {
  // Update the specific chemical's files with the file type
  updateChemicalFiles(files, currentChemicalIndex, type);
};

  useEffect(() => {
    const fetchSession = async () => {
      try {
        const { data } = await supabase.auth.getSession();
        setSession(data?.session);
      } catch (error) {
        console.error("Error fetching session:", error);
      }
    };

    fetchSession();
  }, []);

  // Add this state for sales team members
  const [isLoadingSalesTeam, setIsLoadingSalesTeam] = useState(false);

// Move fetchSalesTeam outside useEffect so it can be reused
    const fetchSalesTeam = async (session: any) => {
      setIsLoadingSalesTeam(true);
      try {
        const { data, error } = await supabase
          .from("user_list")
          .select(
            `
            id,
            email,
            user_roles(role, category)
          `
          )
          .order("email");

        if (error) {
          console.error("Error fetching sales team:", error);
          toast.error("Failed to load sales team members");
          return;
        }

      let newSession = session;

      if (session === null || session === undefined) {
        try {
          const { data } = await supabase.auth.getSession();
          // setSession(data?.session);
          newSession = data?.session;
        } catch (error) {
          console.error("Error fetching session:", error);
        }
      }

        // Transform data to match the format needed for the dropdown
      const formattedTeam = data
        .map((member) => {
          if (member?.email === newSession?.user?.email) {
            return {
              value: member.id,
              label: "Me",
              category: member.user_roles[0]?.category,
            };
          }
          return {
            value: member.id,
            label: member.email,
            category: member.user_roles[0]?.category,
          };
        })
        .sort((a, b) => (a.label === "Me" ? -1 : b.label === "Me" ? 1 : 0));
        // Filter out members with null category or country
        const validMembers = formattedTeam.filter(
          (member) => member.category !== null && member.category !== undefined 
        );

        setSalesTeamMembers(validMembers);
      } catch (error) {
        console.error("Exception fetching sales team:", error);
        toast.error("Failed to load sales team members");
      } finally {
        setIsLoadingSalesTeam(false);
      }
    };

// useEffect to fetch on mount/session change
    useEffect(() => {
      fetchSalesTeam(session);
    }, [session]);

  // Filter sales team members based on user role and category
  const getFilteredSalesTeam = () => {
    // If admin, show all sales team members
    if (userRole === "admin") {
      return salesTeamMembers;
    }
    // If BU head, filter by their category
    else if (userRole === "bu_head" && userCategory) {
      return salesTeamMembers.filter(
        (member) => member.category === userCategory
      );
    }
    // Default case - return empty array
    return [];
  };


  // Handle sales POC selection
  const handleSalesPOCChange = (value: string) => {
    setSelectedSalesPOC(value);

    // If the selected value is "Me", do nothing
    if (value === "Me") {
      return;
    }

    // Find the selected sales team member's category
    const selectedMember = salesTeamMembers.find(
      (member) => member.value === value
    );

    // If a valid member is found, update the chemical data category and selected category
    if (selectedMember && selectedMember.category) {
      // Update the selected category dropdown
      handleCategoryChange(selectedMember.category);
      
      // Update all chemicals with the new category
      chemicals.forEach((_, index) => {
        updateChemicalData({ category: selectedMember.category }, index);
      });
    }
  };

  const getFilteredDestinations = (incoterms: string, country: string) => {
    // Default to US if country is not provided or not in our list
    const countryCode = country && citiesByCountry[country] ? country : "USA";

    let combined: any[] = [];

    if (incoterms && (incoterms.startsWith("CIF") || incoterms.startsWith("CFR") || incoterms.startsWith("FOB"))) {
      // Get user's country ports first
      const userCountryPorts = (portsByCountry[countryCode] || []).map(port => ({
        ...port,
        country: countryCode,
        flag: countryFlags[countryCode as keyof typeof countryFlags]
      }));
      
      // Get all other ports
      const allOtherPorts = Object.entries(portsByCountry)
        .filter(([key]) => key !== countryCode)
        .flatMap(([countryKey, ports]) => 
          ports.map(port => ({
            ...port,
            country: countryKey,
            flag: countryFlags[countryKey as keyof typeof countryFlags]
          }))
        );

      combined = [...userCountryPorts, ...allOtherPorts];
    }
    // For all other incoterms, show cities
    else {
      // Get user's country cities first
      const userCountryCities = (citiesByCountry[countryCode] || []).map(city => ({
        ...city,
        country: countryCode,
        flag: countryFlags[countryCode as keyof typeof countryFlags]
      }));
      
      // Get all other cities
      const allOtherCities = Object.entries(citiesByCountry)
        .filter(([key]) => key !== countryCode)
        .flatMap(([countryKey, cities]) => 
          cities.map(city => ({
            ...city,
            country: countryKey,
            flag: countryFlags[countryKey as keyof typeof countryFlags]
          }))
        );

      combined = [...userCountryCities, ...allOtherCities];
    }

    // Filter to unique values based on the 'value' property
    const seen = new Set();
    const unique = combined?.filter((item) => {
      if (seen.has(item?.value)) return false;
      seen.add(item?.value);
      return true;
    });

    return unique;
  };

  // Function to get destination suggestions based on incoterms and user country
  const getDestinationSuggestions = (incoterms: string) => {
    return getFilteredDestinations(incoterms, userCountry);
  };
  const {
    chemicals,
    customerData,
    isSubmitting,
    showSuccessDialog,
    setShowSuccessDialog,
    updateChemicalData,
    updateCustomerData,
    updateChemicalFiles,
    isFormComplete,
    handleSubmit,
    addNewChemical,
    removeChemical,
    showValidationErrors,
    setShowValidationErrors,
    selectedCustomerId,
    setSelectedCustomerId,
    setSelectedSalesPOC,
    selectedSalesPOC,
    salesTeamMembers,
    setSalesTeamMembers,
    selectedCategory,
    handleCategoryChange
  } = useEnquiryForm();

  // Customer suggestion states
  const [customerSuggestions, setCustomerSuggestions] = useState<string[]>([]);
  const [showSuggestions, setShowSuggestions] = useState(false);
  const [isLoadingSuggestions, setIsLoadingSuggestions] = useState(false);
  const debounceTimerRef = useRef<NodeJS.Timeout | null>(null);
  const dropdownRef = useRef<HTMLUListElement>(null);
  const customerMapRef = useRef<Record<string, any>>({});

  // Replace the old chemical names fetching logic with the new hook
  // const { compounds, isLoading: isLoadingCompounds, fetchCompounds } = useCatalogApi();

  // useEffect(() => {
  //   fetchCompounds();
  // }, [fetchCompounds]);

  // Update the chemical name suggestions state
  const [chemicalNameSuggestions, setChemicalNameSuggestions] = useState<string[]>([]);

  // Add state for chemical name to offset mapping
  const [chemicalNameToOffsetMap, setChemicalNameToOffsetMap] = useState<Map<string, { id: string | null, name: string | null, type: string | null }>>(new Map());

  // Add state for search term
  const [searchTerm, setSearchTerm] = useState('');

  // Add debounce hook
  const debouncedSearchTerm = useDebounce(searchTerm, 300);

  // Effect to trigger search when debounced term changes
  useEffect(() => {
    searchChemicalSuggestions(debouncedSearchTerm);
  }, [debouncedSearchTerm]);


  // Handle chemical name selection
  const handleChemicalNameChange = (value: string, index: number) => {
    // Set the search term for suggestions
    setSearchTerm(value);
    
    // Extract the actual chemical name if it's a synonym (format: "synonym (chemical_name)")
    const match = value.match(/^(.*?)\s*\((.*?)\)$/);
    const actualChemicalName = match ? match[2] : value;
    
    // Update the chemical name in the form
    updateChemicalData({
      chemicalName: value,
      offset_chemical_name: actualChemicalName,
      offset_chemical_id: chemicalNameToOffsetMap.get(actualChemicalName)?.id || null,
      type: chemicalNameToOffsetMap.get(actualChemicalName)?.type || "Others" // Reset type when changing chemical name
    }, index);
  };

  // Add a new effect to handle when search results are ready
  useEffect(() => {
    // When we have search results, check if the current chemical name matches any result
    chemicals.forEach((chemical, index) => {
      if (chemical.chemicalName) {
        const offsetDetails = chemicalNameToOffsetMap.get(chemical.chemicalName);
        if (offsetDetails) {
          // Update the chemical with the correct offset details
          updateChemicalData({
            offset_chemical_id: offsetDetails.id,
            offset_chemical_name: offsetDetails.name
          }, index);
        }
      }
    });
  }, [chemicalNameToOffsetMap,chemicals]); // This will run whenever the map is updated with new search results

  // Handle clicks outside the suggestions dropdown
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        dropdownRef.current &&
        !dropdownRef.current.contains(event.target as Node)
      ) {
        setShowSuggestions(false);
      }
    };

    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, []);

  // Debounced customer search function
  const handleCustomerInputChange = useCallback(
    async (e: React.ChangeEvent<HTMLInputElement>) => {
      const value = e.target.value;
      setSelectedCustomerId(null); // Reset selected customer ID when typing
      updateCustomerData({ customerFullName: value });

      // Clear previous debounce timer
      if (debounceTimerRef.current) {
        clearTimeout(debounceTimerRef.current);
      }

      // Only search if value is long enough
      if (value.length >= 4) {
        setIsLoadingSuggestions(true);
        setShowSuggestions(true);
        debounceTimerRef.current = setTimeout(async () => {
          try {
            // Get current user from localStorage
            const user = {
              email: localStorage.getItem('userEmail'),
              role: localStorage.getItem('userRole'),
              category: localStorage.getItem('userCategory')
            };

            let query = supabase
              .from("customer")
              .select(
                "id, customer_full_name, customer_poc, customer_phone, customer_email, city, country, industries"
              )
              .ilike("customer_full_name", `%${value}%`)
              .order("customer_full_name")
              .limit(10);

            // If sales, only fetch customers for the current user
            if (user?.role === 'sales' && user?.email) {
              query = query.eq('account_owner', user?.email);
            } else if (user?.role === 'bu_head') {
              // First get all users of the category from user_roles
              const { data: categoryUsers, error: usersError } = await supabase
                .from('user_list')
                .select('email, user_roles!inner(*)')
                .eq('user_roles.category', user?.category);

              if (usersError) throw usersError;

              // Get the list of emails from the users
              const accountOwners = categoryUsers?.map(user => user.email) || [];

              // Filter customers by these account owners
              query = query.in('account_owner', accountOwners);
            }

            const { data: customers, error: queryError } = await query.limit(10);

            if (queryError) {
              console.error("Error fetching customers:", queryError);
              throw queryError;
            }

            // Store the full customer data in a map for later use
            const customerMap: Record<string, any> = {};
            customers?.forEach((customer: any) => {
              customerMap[customer.customer_full_name] = customer;
            });
            customerMapRef.current = customerMap;

            // Just show names in suggestions
            const suggestions = customers?.map(
              (customer: any) => customer.customer_full_name
            ) || [];

            setCustomerSuggestions(suggestions);
            setShowSuggestions(true);
          } catch (error) {
            console.error("Error fetching customer suggestions:", error);
            setCustomerSuggestions([]);
          } finally {
            setIsLoadingSuggestions(false);
          }
        }, 300); // 300ms debounce
      } else {
        setCustomerSuggestions([]);
        setShowSuggestions(false);
      }
    },
    [updateCustomerData, setSelectedCustomerId]
  );

  // Handle suggestion click
  const handleSuggestionClick = useCallback(
    async (suggestion: string) => {
      try {

        // Get the customer from the stored map
        const customer = customerMapRef.current[suggestion];

        if (!customer) {
          toast.error("Customer details not found");
          return;
        }

        const customerDetails = customer as unknown as CustomerDetails;
        setSelectedCustomerId(customerDetails.id);
        updateCustomerData({
          customerFullName: customerDetails.customer_full_name,
          customerPoc: customerDetails.customer_poc || "",
          customerPhone: customerDetails.customer_phone || "",
          customerEmail: customerDetails.customer_email || "",
          city: customerDetails.city || "",
          country: customerDetails.country || "",
          industries: customerDetails.industries,
        });

        // Hide suggestions after selection
        setShowSuggestions(false);
      } catch (error) {
        console.error("Error fetching customer details:", error);
        toast.error("Failed to load customer details");
      }
    },
    [updateCustomerData, setSelectedCustomerId]
  );

  const [isExpanded, setIsExpanded] = useState(false);
  const [showMoreFields, setShowMoreFields] = useState<{
    [key: number]: boolean;
  }>({});

  // Handle form submission
  const handleFormSubmit = async () => {
    try {
      // Check authentication first
      const { data: sessionData } = await supabase.auth.getSession();

      if (!sessionData || !sessionData.session) {
        console.error("No active session found");
        toast.error("Please sign in to create enquiries");
        return;
      }
      // ADD THIS VALIDATION BEFORE EXISTING CHECKS:
        if ((userRole === "admin" || userRole === "bu_head") && !selectedSalesPOC) {
          toast.error("Please select a Sales POC");
          setShowValidationErrors(true);
          return;
        }
      // Check form completion
      if (!isFormComplete()) {
        console.log("Form is incomplete, showing validation errors");
        setShowValidationErrors(true);
        toast.error(
          "Please fill in all required fields before creating the enquiry"
        );
        return;
      }

      const allFiles = chemicals.flatMap((chemical) => chemical.attachedFiles);
      try {
        const result = await handleSubmit(allFiles);

        if (result && typeof result === 'object' && 'success' in result && result.success) {
          toast.success("Enquiry created successfully!");
          // Add a slight delay before refreshing the page to allow the success message to be seen
          setTimeout(() => {
            window.location.reload();
          }, 1500);
        } else {
          console.error("Submission failed:", result);
          toast.error(
            "Failed to create enquiry: " + (result && typeof result === 'object' && 'error' in result ? result.error : "Unknown error")
          );
        }
      } catch (submitError) {
        console.error("Exception in handleSubmit:", submitError);
        toast.error(
          "Exception during enquiry creation: " +
            (submitError.message || "Unknown error")
        );
      }
    } catch (error) {
      console.error("Error in form submission:", error);
      toast.error(
        "Failed to create enquiry: " + (error.message || "Unknown error")
      );
    } finally {
      setSelectedSalesPOC(""); // Reset selected POC after submission
    }
  };

  // Toggle more fields for a specific chemical
  // Only one chemical can be expanded at a time
  const toggleMoreFields = (index: number) => {
    setShowMoreFields((prev) => {
      // Create a new object with all values set to false
      const newState: { [key: number]: boolean } = {};

      // If the clicked index is already open, close it
      // Otherwise, open only the clicked index
      if (prev[index]) {
        // If already open, close it
        return newState;
      } else {
        // Close all, then open only the clicked one
        newState[index] = true;
        return newState;
      }
    });
  };

  // Add new state for account owners
  const [accountOwners, setAccountOwners] = useState<string[]>([]);

  // Add effect to fetch account owners once when component mounts
  useEffect(() => {
    const fetchAccountOwners = async () => {
      const user = {
        email: localStorage.getItem('userEmail'),
        role: localStorage.getItem('userRole'),
        category: localStorage.getItem('userCategory')
      };

      // Only fetch if user is BU head
      if (user?.role === 'bu_head' && user?.category) {
        try {
          const { data: categoryUsers, error: usersError } = await supabase
            .from('user_list')
            .select('email, user_roles!inner(*)')
            .eq('user_roles.category', user.category);

          if (usersError) throw usersError;

          const owners = categoryUsers?.map(user => user.email) || [];
          setAccountOwners(owners);
        } catch (error) {
          console.error("Error fetching account owners:", error);
          setAccountOwners([]);
        }
      }
    };

    fetchAccountOwners();
  }, []); // Empty dependency array means this runs once on mount

  // Then modify the searchChemicalSuggestions function to use the stored accountOwners
  const searchChemicalSuggestions = async (searchTerm: string) => {
    if (!searchTerm || searchTerm.length < 2) {
      setChemicalNameSuggestions([]);
      return;
    }

    try {
      // Search catalog API with the search term
      const catalogResults = await axios.get(
        `${API_CONFIG.catalogBaseUrl}catalog`,
        {
          params: { q: searchTerm },
          headers: {
            'accept': 'application/json',
            'Authorization': `Bearer ${API_CONFIG.accessToken}`
          }
        }
      );

      // Process catalog results
      const catalogResultsWithSynonyms = catalogResults?.data?.data?.map(compound => ({
        displayName: compound.synonym ? `${compound.synonym} (${compound.chemical_name})` : compound.chemical_name,
        chemicalName: compound.chemical_name,
        compoundId: compound.compound_id,
        synonym: compound.synonym,
        chemical_type: compound.chemical_type || "Others", // Default to "other" if type is not provided
      })) || [];

      // Store mapping of chemical names to their offset details
      const newChemicalNameToOffsetMap = new Map<string, { id: string | null, name: string | null, type: string | null  }>(
        // Add catalog mappings with both chemical name and synonym
        catalogResultsWithSynonyms.flatMap(compound => [
          // Map for chemical name
          [compound.chemicalName, {
            id: compound.compoundId,
            name: compound.chemicalName,
            type: compound.chemical_type || "Others" // Default to "other" if type is not provided
          }],
          // Map for synonym if it exists
          ...(compound.synonym ? [[compound.synonym, {
            id: compound.compoundId,
            name: compound.chemicalName,
            type: compound.chemical_type || "Others" // Default to "other" if type is not provided
          }]] : [])
        ])
      );

      setChemicalNameToOffsetMap(newChemicalNameToOffsetMap);

      // Set suggestions with synonyms
      setChemicalNameSuggestions(catalogResultsWithSynonyms.map(item => item.displayName));
    } catch (error) {
      console.error("Error searching chemical suggestions:", error);
      setChemicalNameSuggestions([]);
      setChemicalNameToOffsetMap(new Map());
    }
  };

  // Handle destination selection
  const handleDestinationChange = (value: string, index: number) => {
    // Find the selected destination's country from the suggestions
    const selectedDestination = getDestinationSuggestions(chemicals[index].incoterms)
      .find(option => option.value === value);

    // Update both destination and its country
    updateChemicalData({
      destination: value,
      destination_country: selectedDestination?.country || null
    }, index);
  };

  return (
    <div className="enquiry-form w-full mx-auto bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
      {/* Header with expand/collapse */}
      <div
        className="flex items-center justify-between p-4 bg-white border-b border-gray-200 cursor-pointer"
        onClick={() => setIsExpanded(!isExpanded)}
      >
        <div className="flex items-center gap-2">
          <Plus className="h-5 w-5 text-[#294d48] font-bold stroke-[2.5px]" />
          <span className="text-xl font-semibold text-[#294d48]">
            Create New Enquiry
          </span>
        </div>
        <div>
          {isExpanded ? (
            <ChevronUp className="h-5 w-5 text-gray-500" />
          ) : (
            <ChevronDown className="h-5 w-5 text-gray-500" />
          )}
        </div>
      </div>

      {/* Form content */}
      {isExpanded && (
        <div className="p-6">
          {/* Customer Name and Sales POC section */}
          <div className="mb-6">
            <div
              className={`grid grid-cols-2 ${
                userRole === "admin" || userRole === "bu_head"
                  ? "md:grid-cols-3"
                  : ""
              } gap-4`}
            >
              {/* Customer Name - with suggestions */}
              <div className="relative">
                <Label
                  htmlFor="customerName"
                  className="block text-sm font-medium mb-1"
                >
                  Customer Name
                </Label>
                <Input
                  id="customerName"
                  placeholder="Enter customer name"
                  value={customerData.customerFullName}
                  onChange={handleCustomerInputChange}
                  className={`w-full ${
                    showValidationErrors && !customerData.customerFullName
                      ? "border-red-500"
                      : ""
                  }`}
                />

                {/* Customer suggestions dropdown */}
                {showSuggestions && (
                  <ul
                    ref={dropdownRef}
                    className="absolute bg-white border border-gray-200 w-full mt-1 max-h-40 overflow-y-auto z-10 rounded-md shadow-lg"
                  >
                    {isLoadingSuggestions ? (
                      <li className="p-2 text-center text-gray-500">
                        Loading...
                      </li>
                    ) : customerSuggestions.length > 0 ? (
                      customerSuggestions.map((suggestion, index) => (
                        <li
                          key={index}
                          className={`p-2 cursor-pointer ${
                            index % 2 === 0 ? "bg-gray-50" : "bg-white"
                          } hover:bg-blue-50`}
                          onClick={() => handleSuggestionClick(suggestion)}
                        >
                          {suggestion}
                        </li>
                      ))
                    ) : (
                      <li className="p-2 text-center text-gray-500">
                        No customers found. Continue typing to create a new one.
                      </li>
                    )}
                  </ul>
                )}
              </div>


              {/* Sales POC dropdown - only for admins and BU heads */}
              {(userRole === "admin" || userRole === "bu_head") && (
                <div>
                  <Label
                    htmlFor="salesPOC"
                    className="block text-sm font-medium mb-1"
                  >
                    Sales POC Name <span className="text-red-500">*</span>
                  </Label>
                  <Select
                    value={selectedSalesPOC}
                    onValueChange={handleSalesPOCChange}
                  >
                   <SelectTrigger 
                      className={`w-full h-10 text-sm ${
                        showValidationErrors && (userRole === "admin" || userRole === "bu_head") && !selectedSalesPOC
                          ? "border-red-500"
                          : ""
                      }`}
                      style={{
                        fontSize: '16px',
                        color: selectedSalesPOC ? 'inherit' : '#9CA3AF'
                      }}
                    >
                    <SelectValue placeholder="Select Sales POC" />
                    </SelectTrigger>
                    <SelectContent>
                      {/* <SelectItem value="Me">Me</SelectItem> */}
                      {getFilteredSalesTeam().map((member) => (
                        <SelectItem key={member.value} value={member.value}>
                          {member.label}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              )}

              {/* Category dropdown */}
            <div>
                <Label
                  htmlFor="category"
                  className="block text-sm font-medium mb-1"
                >
                  Category
                </Label>
                <Select
                  value={selectedCategory}
                  onValueChange={handleCategoryChange}
                  disabled={true}
                >
                  <SelectTrigger
                  >
                    <SelectValue placeholder="Select Category" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="Agro">Agro</SelectItem>
                    <SelectItem value="Coatings">Coatings</SelectItem>
                    <SelectItem value="Construction">Construction</SelectItem>
                    <SelectItem value="FoodAdditives">Food Additives</SelectItem>
                    <SelectItem value="OilGas">Oil & Gas</SelectItem>
                    <SelectItem value="PersonalCare">Personal Care</SelectItem>
                    <SelectItem value="Pharmaceuticals">Pharmaceuticals</SelectItem>
                  </SelectContent>
                </Select>
              </div>

            </div>
          </div>

          {/* Chemicals Section */}
          <div className="mb-6">
            <h4 className="text-sm font-medium mb-4">Chemicals</h4>

            {/* Horizontal scrolling container */}
            <div className="overflow-x-auto">
              <div className="min-w-[1200px]">
                {/* Table Header Row */}
                <div className="grid grid-cols-7 gap-4 p-3 bg-gray-50 border border-gray-200 rounded-t-lg">
                  {/* Column widths: 20%, 15%, 15%, 15%, 15%, 10%, 10% */}
                  <div className="block text-sm font-medium">
                    Chemical Name <span className="text-red-500">*</span>
                  </div>
                  <div className="text-sm font-medium">
                    Quantity <span className="text-red-500">*</span>
                  </div>
                  <div className="text-sm font-medium">
                    Incoterms <span className="text-red-500">*</span>
                  </div>
                  <div className="text-sm font-medium">
                    Destination <span className="text-red-500">*</span>
                  </div>
                  <div className="text-sm font-medium">Target Price</div>
                  <div className="text-sm font-medium">
                    Documents from Customer
                  </div>
                  <div className="text-sm font-medium">
                    <span>
                      Criticality <span className="text-red-500">*</span>
                    </span>
                  </div>
                </div>

                {/* Chemical Rows */}
                {chemicals.map((chemical, index) => (
                  <div
                    key={index}
                    className={`border border-t-0 border-gray-200 ${
                      showMoreFields[index] ? "bg-gray-200" : ""
                    }`}
                  >
                    <div
                      className={`grid grid-cols-7 gap-4 p-3 ${
                        showMoreFields[index] ? "bg-gray-200" : ""
                      }`}
                    >
                      {/* Chemical Name */}
                      <div
                        className={`border rounded-md ${
                          showValidationErrors && !chemical.chemicalName
                            ? "border-red-500"
                            : ""
                        }`}
                        style={{
                          borderColor:
                            showValidationErrors && !chemical.chemicalName
                              ? "#ef4444"
                              : "hsl(214.3 31.8% 91.4%)",
                        }}>
                        <AutoComplete
                          id={`chemicalName-${index}`}
                          placeholder="Chemical name"
                          value={chemical.chemicalName || ""}
                          onChange={(value) => handleChemicalNameChange(value, index)}
                          options={chemicalNameSuggestions.map(name => ({ value: name }))}
                          filterOption={(inputValue: string, option: any) => {
                            if (!option?.value) return false;
                            const searchTerm = inputValue.toLowerCase();
                            const optionValue = option.value.toLowerCase();
                            
                            // Exact match
                            if (optionValue.includes(searchTerm)) return true;
                            
                            // Fuzzy match - check if all characters in search term appear in order
                            const searchChars = searchTerm.split('');
                            let lastIndex = -1;
                            return searchChars.every(char => {
                              const index = optionValue.indexOf(char, lastIndex + 1);
                              if (index === -1) return false;
                              lastIndex = index;
                              return true;
                            });
                          }}
                          style={{
                            width: "100%",
                            height: "40px",
                            border: "none",
                            boxShadow: "none",
                          }}
                          className={`w-full ${
                            showValidationErrors && !chemical.chemicalName
                              ? "border-red-500"
                              : ""
                          }`}
                        />
                      </div>

                      {/* Quantity */}
                      <div>
                        <div className="flex">
                          <Input
                            id={`quantity-${index}`}
                            type="number"
                            min="0"
                            placeholder="Qty"
                            value={
                              chemical.quantity !== null
                                ? chemical.quantity
                                : ""
                            }
                            onChange={(e) =>
                              updateChemicalData(
                                {
                                  quantity: e.target.value
                                    ? Number(e.target.value)
                                    : null,
                                },
                                index
                              )
                            }
                            className={`quantity-input rounded-r-none ${
                              showValidationErrors && chemical.quantity === null
                                ? "border-red-500"
                                : ""
                            }`}
                          />
                          <AntSelect
                            value={chemical.quantityUnit || undefined}
                            onChange={(value) =>
                              updateChemicalData({ quantityUnit: value }, index)
                            }
                            className="quantity-select rounded-l-none border border-input border-l-0 w-[100px] h-10"
                            style={{
                              backgroundColor: "white",
                              borderColor: "#e5e7eb",
                              display: "flex",
                              alignItems: "center",
                            }}
                            placeholder="Unit"
                            optionLabelProp="label"
                            popupMatchSelectWidth={false}
                            dropdownStyle={{ minWidth: "150px" }}
                          >
                            <AntSelect.Option
                              value="Metric Ton (mt)"
                              label="Mt"
                            >
                              Metric Ton (mt)
                            </AntSelect.Option>
                            <AntSelect.Option value="Pound (lb)" label="Pound">
                              Pound (lb)
                            </AntSelect.Option>
                            <AntSelect.Option
                              value="Gallon (gal)"
                              label="Gallon"
                            >
                              Gallon (gal)
                            </AntSelect.Option>
                            <AntSelect.Option value="Litre (L)" label="Litre">
                              Litre (L)
                            </AntSelect.Option>
                            <AntSelect.Option value="Kilolitre (Kl)" label="Kl">
                              Kilolitre (Kl)
                            </AntSelect.Option>
                            <AntSelect.Option value="Kilogram (Kg)" label="Kg">
                              Kilogram (Kg)
                            </AntSelect.Option>
                            <AntSelect.Option value="FTL" label="FTL">
                              FTL
                            </AntSelect.Option>
                            <AntSelect.Option value="IBC" label="IBC">
                              IBC
                            </AntSelect.Option>
                            <AntSelect.Option value="Drums" label="Drums">
                              Drums
                            </AntSelect.Option>
                            <AntSelect.Option value="ISO" label="Kl">
                              ISO
                            </AntSelect.Option>
                          </AntSelect>
                        </div>
                      </div>

                      {/* Incoterms */}
                      <div>
                        <AntSelect
                          value={chemical.incoterms || undefined}
                          onChange={(value) =>
                            updateChemicalData({ incoterms: value }, index)
                          }
                          className={`w-full h-10 border border-input ${
                            showValidationErrors && !chemical.incoterms
                              ? "border-red-500"
                              : ""
                          }`}
                          style={{
                            backgroundColor: "white",
                            display: "flex",
                            alignItems: "center",
                          }}
                          placeholder="Incoterms"
                          optionLabelProp="label"
                          popupMatchSelectWidth={false}
                          dropdownStyle={{ minWidth: "250px" }}
                        >
                          <AntSelect.Option value="EXW (Ex Works)" label="EXW">
                            EXW (Ex Works)
                          </AntSelect.Option>
                          <AntSelect.Option
                            value="FOB (Free on board)"
                            label="FOB"
                          >
                            FOB (Free on board)
                          </AntSelect.Option>
                          <AntSelect.Option
                            value="DDP (Delivery Duty Paid)"
                            label="DDP"
                          >
                            DDP (Delivery Duty Paid)
                          </AntSelect.Option>
                          <AntSelect.Option
                            value="CIF (Cost, Insurance, & Freight)"
                            label="CIF"
                          >
                            CIF (Cost, Insurance, & Freight)
                          </AntSelect.Option>
                          <AntSelect.Option
                            value="CFR (Cost & Freight)"
                            label="CFR"
                          >
                            CFR (Cost & Freight)
                          </AntSelect.Option>
                          <AntSelect.Option
                            value="FOR (Free on road)"
                            label="FOR"
                          >
                            FOR (Free on road)
                          </AntSelect.Option>
                        </AntSelect>
                      </div>

                      {/* Destination */}
                      <div>
                        {chemical.incoterms ? (
                          <div
                            className={`border rounded-md ${
                              showValidationErrors && !chemical.destination
                                ? "border-red-500"
                                : ""
                            }`}
                            style={{
                              borderColor:
                                showValidationErrors && !chemical.destination
                                  ? "#ef4444"
                                  : "hsl(214.3 31.8% 91.4%)",
                            }}
                          >
                            <AutoComplete
                              id={`destination-${index}`}
                              placeholder="Destination"
                              value={chemical.destination || ""}
                              onChange={(value) => handleDestinationChange(value, index)}
                              options={getDestinationSuggestions(chemical.incoterms).map(option => ({
                                value: option.value,
                                label: (
                                  <div className="flex items-center gap-2">
                                    <span className="text-lg">{option.flag}</span>
                                    <span>{option.value}</span>
                                  </div>
                                )
                              }))}
                              filterOption={(inputValue, option) =>
                                option!.value.toString().toLowerCase().includes(inputValue.toLowerCase())
                              }
                              style={{
                                width: "100%",
                                height: "40px",
                                border: "none",
                                boxShadow: "none",
                              }}
                            />
                          </div>
                        ) : (
                          <Input
                            id={`destination-${index}`}
                            placeholder="Destination"
                            value={chemical.destination || ""}
                            onChange={(e) =>
                              updateChemicalData(
                                { 
                                  destination: e.target.value,
                                  destination_country: null // Reset country when manually entering destination
                                },
                                index
                              )
                            }
                            className={`w-full ${
                              showValidationErrors && !chemical.destination
                                ? "border-red-500"
                                : ""
                            }`}
                          />
                        )}
                      </div>

                      {/* Target Price */}
                  <div>
                    <div className="flex items-center">
                      {/* Price Input - Takes much more space */}
                <div className="flex-1" style={{ flex: '1.5', minWidth: '30px' }}>
                              <Input
                              type="number"
                              min="0"
                              step="0.01"
                              id={`targetPrice-${index}`}
                              placeholder="Enter price"
                              value={
                                chemical.targetPrice !== null
                                ? chemical.targetPrice
                                : ""
                              }
                              onChange={(e) =>
                                updateChemicalData(
                                {
                                  targetPrice: e.target.value
                                  ? Number(e.target.value)
                                  : null,
                                },
                                index
                                )
                              }
                              className="w-full rounded-r-none text-sm px-2"
                              style={{
                                minWidth: '80px',
                                height: '40px'
                              }}
                              />
                            </div>
                            
                            {/* Currency Select - Reduced space */}
                            <div style={{ flex: '0.5', minWidth: '75px' }}>
                              <AntSelect
                                value={chemical.targetPriceCurrency || undefined}
                                onChange={(value) =>
                                  updateChemicalData(
                                    { targetPriceCurrency: value },
                                    index
                                  )
                                }
                                className="w-full rounded-none border border-input border-l-0"
                                style={{
                                  backgroundColor: "white",
                                  borderColor: "#e5e7eb",
                                  display: "flex",
                                  alignItems: "center",
                                  height: '40px'
                                }}
                                placeholder="Curr"
                                optionLabelProp="label"
                                popupMatchSelectWidth={false}
                                dropdownStyle={{ minWidth: "120px" }}
                              >
                                <AntSelect.Option value="USD" label="USD $">
                                  USD $
                                </AntSelect.Option>
                                <AntSelect.Option value="INR" label="INR ₹">
                                  INR ₹
                                </AntSelect.Option>
                                <AntSelect.Option value="EURO" label="EUR €">
                                  EUR €
                                </AntSelect.Option>
                                <AntSelect.Option value="YUAN" label="CNY ¥">
                                  CNY ¥
                                </AntSelect.Option>
                                <AntSelect.Option value="YEN" label="JPY ¥">
                                  JPY ¥
                                </AntSelect.Option>
                                <AntSelect.Option value="AED" label="AED د.إ">
                                  AED د.إ
                                </AntSelect.Option>
                              </AntSelect>
                            </div>
                            
                            {/* Unit Display - More compact */}
                            {chemical.quantityUnit && (
                              <div 
                                className="flex items-center justify-center border border-input border-l-0 rounded-r-md bg-gray-50 px-1" 
                                style={{ 
                                  flex: '0.8',
                                  minWidth: '30px',
                                  height: '40px'
                                }}
                              >
                                <span className="text-gray-600 text-xs mr-1">/</span>
                                <span className="text-xs font-medium text-gray-700">
                                  {(() => {
                                    const unitMap = {
                                      "Kilogram (Kg)": "Kg",
                                      "Metric Ton (mt)": "Mt",
                                      "Pound (lb)": "lb",
                                      "Gallon (gal)": "gal",
                                      "Litre (L)": "L",
                                      "Kilolitre (Kl)": "Kl",
                                      "FTL": "FTL",
                                      "IBC": "IBC",
                                      "Drums": "Drums",
                                      "ISO": "ISO"
                                    };
                                    return unitMap[chemical.quantityUnit as keyof typeof unitMap] || chemical.quantityUnit;
                                  })()}
                                </span>
                              </div>
                            )}
                          </div>
                        </div>

                      {/* Document Upload */}
                      <div>
                    <Button
                        type="button"
                        variant="outline"
                        size="sm"
                        onClick={() => {
                          setCurrentChemicalIndex(index);
                          // Load existing files for this chemical into filesByType format
                          const chemical = chemicals[index];
                          const organizedFiles = {
                            tds: chemical.attachedFiles?.filter((f) => f.fileType === "tds") || [],
                            sds: chemical.attachedFiles?.filter((f) => f.fileType === "sds") || [],
                            coa: chemical.attachedFiles?.filter((f) => f.fileType === "coa") || [],
                            moa: chemical.attachedFiles?.filter((f) => f.fileType === "moa") || [],
                            others: chemical.attachedFiles?.filter(
                                (f) => f.fileType === "others"
                              ) || [],
                          };
                          setFilesByType(organizedFiles);
                          setShowDocModal(true);
                        }}

                            className="flex items-center text-sm justify-center w-full h-10 px-4" // 🔥 Updated size

                      >
                        <Upload className="h-4 w-4 mr-1" />
                        Upload
                      </Button>
                      </div>

                      {/* Criticality with Delete and Expand buttons in the same row */}
                      <div>
                        <div className="flex items-center justify-between">
                          <div className="flex-grow">
                            <AntSelect
                              value={chemical.criticality || "medium"}
                              onChange={(value) =>
                                updateChemicalData(
                                  {
                                    criticality: value as
                                      | "high"
                                      | "medium"
                                      | "low",
                                  },
                                  index
                                )
                              }
                              className="w-full h-10 border border-input"
                              style={{
                                backgroundColor: "white",
                                display: "flex",
                                alignItems: "center",
                              }}
                              placeholder="Criticality"
                              optionLabelProp="label"
                            >
                              <AntSelect.Option value="high" label="High">
                                High
                              </AntSelect.Option>
                              <AntSelect.Option value="medium" label="Medium">
                                Medium
                              </AntSelect.Option>
                              <AntSelect.Option value="low" label="Low">
                                Low
                              </AntSelect.Option>
                            </AntSelect>
                          </div>

                          <div className="flex items-center ml-2">
                            {/* Delete Chemical Button */}
                            {chemicals.length > 1 && (
                              <button
                                type="button"
                                onClick={() => removeChemical(index)}
                                className="text-gray-500 hover:text-red-500 transition-colors"
                                title="Delete this chemical"
                              >
                                <svg
                                  xmlns="http://www.w3.org/2000/svg"
                                  width="16"
                                  height="16"
                                  viewBox="0 0 24 24"
                                  fill="none"
                                  stroke="currentColor"
                                  strokeWidth="2"
                                  strokeLinecap="round"
                                  strokeLinejoin="round"
                                >
                                  <path d="M3 6h18" />
                                  <path d="M19 6v14a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2V6m3 0V4a2 2 0 0 1 2-2h4a2 2 0 0 1 2 2v2" />
                                </svg>
                              </button>
                            )}

                            <Button
                              type="button"
                              variant="ghost"
                              size="sm"
                              onClick={() => toggleMoreFields(index)}
                              className="ml-1"
                            >
                              {showMoreFields[index] ? (
                                <ChevronUp className="h-4 w-4" />
                              ) : (
                                <ChevronDown className="h-4 w-4" />
                              )}
                            </Button>
                          </div>
                        </div>
                      </div>
                    </div>

                    {/* Additional fields (shown when "More" is clicked) */}
                    {showMoreFields[index] && (
                      <div className="grid grid-cols-7 gap-4 p-3 bg-gray-200">
                        {/* Packaging Type */}
                        <div>
                          <Label
                            htmlFor={`packagingType-${index}`}
                            className="block text-sm font-medium mb-1"
                          >
                            Packaging Type
                          </Label>
                          <AntSelect
                            id={`packagingType-${index}`}
                            value={chemical.packagingType || undefined}
                            onChange={(value) =>
                              updateChemicalData(
                                { packagingType: value },
                                index
                              )
                            }
                            className="w-full h-10 border border-input"
                            style={{
                              backgroundColor: "white",
                              display: "flex",
                              alignItems: "center",
                            }}
                            placeholder="Packaging Type"
                            optionLabelProp="label"
                            popupMatchSelectWidth={false}
                            dropdownStyle={{ minWidth: "200px" }}
                          >
                            <AntSelect.Option value="ISO TANK" label="ISO TANK">ISO TANK</AntSelect.Option>
                            <AntSelect.Option value="IBC" label="IBC">IBC</AntSelect.Option>
                            <AntSelect.Option value="HDPE DRUM OPEN TOP" label="HDPE DRUM OPEN TOP">HDPE DRUM OPEN TOP</AntSelect.Option>
                            <AntSelect.Option value="HDPE DRUM NARROW MOUTH" label="HDPE DRUM NARROW MOUTH">HDPE DRUM NARROW MOUTH</AntSelect.Option>
                            <AntSelect.Option value="JERRY CAN" label="JERRY CAN">JERRY CAN</AntSelect.Option>
                            <AntSelect.Option value="FIBRE BOARD DRUM" label="FIBRE BOARD DRUM">FIBRE BOARD DRUM</AntSelect.Option>
                            <AntSelect.Option value="BAG" label="BAG">BAG</AntSelect.Option>
                            <AntSelect.Option value="JUMBO BAG" label="JUMBO BAG">JUMBO BAG</AntSelect.Option>
                            <AntSelect.Option value="CARTON BOX" label="CARTON BOX">CARTON BOX</AntSelect.Option>
                            <AntSelect.Option value="CARDBOARD DRUM" label="CARDBOARD DRUM">CARDBOARD DRUM</AntSelect.Option>
                            <AntSelect.Option value="HDPE BAG" label="HDPE BAG">HDPE BAG</AntSelect.Option>
                            <AntSelect.Option value="MS DRUM" label="MS DRUM">MS DRUM</AntSelect.Option>
                            <AntSelect.Option value="FIBRE DRUM OPEN TOP" label="FIBRE DRUM OPEN TOP">FIBRE DRUM OPEN TOP</AntSelect.Option>
                            <AntSelect.Option value="TANKER TRUCK" label="TANKER TRUCK">TANKER TRUCK</AntSelect.Option>
                            <AntSelect.Option value="CARBOYS" label="CARBOYS">CARBOYS</AntSelect.Option>
                            <AntSelect.Option value="HDPE DRUM" label="HDPE DRUM">HDPE DRUM</AntSelect.Option>
                            <AntSelect.Option value="PP BAG" label="PP BAG">PP BAG</AntSelect.Option>
                            <AntSelect.Option value="HDPE BOTTLE" label="HDPE BOTTLE">HDPE BOTTLE</AntSelect.Option>
                            <AntSelect.Option value="BOTTLE" label="BOTTLE">BOTTLE</AntSelect.Option>
                            <AntSelect.Option value="PACKET" label="PACKET">PACKET</AntSelect.Option>
                            <AntSelect.Option value="FLEXI BAG" label="FLEXI BAG">FLEXI BAG</AntSelect.Option>
                            <AntSelect.Option value="STEEL DRUM" label="STEEL DRUM">STEEL DRUM</AntSelect.Option>
                            <AntSelect.Option value="PAPER BAG" label="PAPER BAG">PAPER BAG</AntSelect.Option>
                          </AntSelect>
                        </div>

                        {/* Qty per Packaging */}
                        <div>
                          <Label
                            htmlFor={`qtyPerPackaging-${index}`}
                            className="block text-sm font-medium mb-1"
                          >
                            Qty per Packaging
                          </Label>
                          <div className="flex">
                            <Input
                              id={`qtyPerPackaging-${index}`}
                              type="number"
                              min="0"
                              placeholder="Qty per Packaging"
                              value={chemical.qtyPerPackaging !== null ? chemical.qtyPerPackaging : ""}
                              onChange={(e) =>
                                updateChemicalData(
                                  { qtyPerPackaging: e.target.value ? Number(e.target.value) : null },
                                  index
                                )
                              }
                              className="quantity-input rounded-r-none"
                            />
                            <AntSelect
                              value={chemical.qtyPerPackagingUnit || undefined}
                              onChange={(value) =>
                                updateChemicalData({ qtyPerPackagingUnit: value }, index)
                              }
                              className="quantity-select rounded-l-none border border-input border-l-0 w-[100px] h-10"
                              style={{
                                backgroundColor: "white",
                                borderColor: "#e5e7eb",
                                display: "flex",
                                alignItems: "center",
                              }}
                              placeholder="Unit"
                              optionLabelProp="label"
                              popupMatchSelectWidth={false}
                              dropdownStyle={{ minWidth: "150px" }}
                            >
                           <AntSelect.Option
                              value="Metric Ton (mt)"
                              label="Mt"
                            >
                              Metric Ton (mt)
                            </AntSelect.Option>
                            <AntSelect.Option value="Pound (lb)" label="Pound">
                              Pound (lb)
                            </AntSelect.Option>
                            <AntSelect.Option
                              value="Gallon (gal)"
                              label="Gallon"
                            >
                              Gallon (gal)
                            </AntSelect.Option>
                            <AntSelect.Option value="Litre (L)" label="Litre">
                              Litre (L)
                            </AntSelect.Option>
                            <AntSelect.Option value="Kilolitre (Kl)" label="Kl">
                              Kilolitre (Kl)
                            </AntSelect.Option>
                            <AntSelect.Option value="Kilogram (Kg)" label="Kg">
                              Kilogram (Kg)
                            </AntSelect.Option>
                            </AntSelect>
                          </div>
                        </div>

                        {/* CAS Number */}
                        <div>
                          <Label
                            htmlFor={`casNumber-${index}`}
                            className="block text-sm font-medium mb-1"
                          >
                            CAS Number
                          </Label>
                          <Input
                            id={`casNumber-${index}`}
                            placeholder="CAS Number"
                            value={chemical.casNumber || ""}
                            onChange={(e) =>
                              updateChemicalData(
                                { casNumber: e.target.value },
                                index
                              )
                            }
                            className="w-full"
                          />
                        </div>

                        {/* Application */}
                        <div>
                          <Label
                            htmlFor={`application-${index}`}
                            className="block text-sm font-medium mb-1"
                          >
                            Application
                          </Label>
                          <Input
                            id={`application-${index}`}
                            placeholder="Application"
                            value={chemical.application || ""}
                            onChange={(e) =>
                              updateChemicalData(
                                { application: e.target.value },
                                index
                              )
                            }
                            className="w-full"
                          />
                        </div>

                        {/* Procurement Volume */}
                        <div>
                          <Label
                            htmlFor={`procurementVolume-${index}`}
                            className="block text-sm font-medium mb-1"
                          >
                            Cust. Proc. (Quarterly)
                          </Label>
                          <div className="flex">
                            <Input
                              id={`procurementVolume-${index}`}
                              type="number"
                              min="0"
                              placeholder="Annual volume"
                              value={
                                chemical.procurementVolume !== null
                                  ? chemical.procurementVolume
                                  : ""
                              }
                              onChange={(e) =>
                                updateChemicalData(
                                  {
                                    procurementVolume: e.target.value
                                      ? Number(e.target.value)
                                      : null,
                                  },
                                  index
                                )
                              }
                              className="quantity-input rounded-r-none"
                            />
                            <AntSelect
                              value={chemical.procurementUnit || undefined}
                              onChange={(value) =>
                                updateChemicalData(
                                  { procurementUnit: value },
                                  index
                                )
                              }
                              className="quantity-select rounded-l-none border border-input border-l-0 w-[100px] h-10"
                              style={{
                                backgroundColor: "white",
                                borderColor: "#e5e7eb",
                                display: "flex",
                                alignItems: "center",
                              }}
                              placeholder="Unit"
                              optionLabelProp="label"
                              popupMatchSelectWidth={false}
                              dropdownStyle={{ minWidth: "150px" }}
                            >
                              <AntSelect.Option
                                value="Metric Ton (mt)"
                                label="Metric Ton"
                              >
                                Metric Ton (mt)
                              </AntSelect.Option>
                              <AntSelect.Option
                                value="Pound (lb)"
                                label="Pound"
                              >
                                Pound (lb)
                              </AntSelect.Option>
                              <AntSelect.Option
                                value="Gallon (gal)"
                                label="Gallon"
                              >
                                Gallon (gal)
                              </AntSelect.Option>
                              <AntSelect.Option value="Litre (L)" label="Litre">
                                Litre (L)
                              </AntSelect.Option>
                              <AntSelect.Option
                                value="Kilolitre (Kl)"
                                label="Kl"
                              >
                                Kilolitre (Kl)
                              </AntSelect.Option>
                              <AntSelect.Option
                                value="Kilogram (Kg)"
                                label="Kg"
                              >
                                Kilogram (Kg)
                              </AntSelect.Option>
                            </AntSelect>
                          </div>
                        </div>

                        {/* Expected Volume */}
                        <div>
                          <Label
                            htmlFor={`expectedVolume-${index}`}
                            className="block text-sm font-medium mb-1"
                          >
                            Exp. Proc. (Quarterly)
                          </Label>
                          <div className="flex">
                            <Input
                              id={`expectedVolume-${index}`}
                              type="number"
                              min="0"
                              placeholder="Expected volume"
                              value={
                                chemical.expectedprocurementVolume !== null
                                  ? chemical.expectedprocurementVolume
                                  : ""
                              }
                              onChange={(e) =>
                                updateChemicalData(
                                  {
                                    expectedprocurementVolume: e.target.value
                                      ? Number(e.target.value)
                                      : null,
                                  },
                                  index
                                )
                              }
                              className="quantity-input rounded-r-none"
                            />
                            <AntSelect
                              value={
                                chemical.expectedprocurementUnit || undefined
                              }
                              onChange={(value) =>
                                updateChemicalData(
                                  { expectedprocurementUnit: value },
                                  index
                                )
                              }
                              className="quantity-select rounded-l-none border border-input border-l-0 w-[100px] h-10"
                              style={{
                                backgroundColor: "white",
                                borderColor: "#e5e7eb",
                                display: "flex",
                                alignItems: "center",
                              }}
                              placeholder="Unit"
                              optionLabelProp="label"
                              popupMatchSelectWidth={false}
                              dropdownStyle={{ minWidth: "150px" }}
                            >
                              <AntSelect.Option
                                value="Metric Ton (mt)"
                                label="Metric Ton"
                              >
                                Metric Ton (mt)
                              </AntSelect.Option>
                              <AntSelect.Option
                                value="Pound (lb)"
                                label="Pound"
                              >
                                Pound (lb)
                              </AntSelect.Option>
                              <AntSelect.Option
                                value="Gallon (gal)"
                                label="Gallon"
                              >
                                Gallon (gal)
                              </AntSelect.Option>
                              <AntSelect.Option value="Litre (L)" label="Litre">
                                Litre (L)
                              </AntSelect.Option>
                              <AntSelect.Option
                                value="Kilolitre (Kl)"
                                label="Kl"
                              >
                                Kilolitre (Kl)
                              </AntSelect.Option>
                              <AntSelect.Option
                                value="Kilogram (Kg)"
                                label="Kg"
                              >
                                Kilogram (Kg)
                              </AntSelect.Option>
                            </AntSelect>
                          </div>
                        </div>

                        {/* Remarks */}
                        <div>
                          <Label
                            htmlFor={`remarks-${index}`}
                            className="block text-sm font-medium mb-1"
                          >
                            Remarks
                          </Label>
                          <Input
                            id={`remarks-${index}`}
                            placeholder="Remarks"
                            value={chemical.remarks || ""}
                            onChange={(e) =>
                              updateChemicalData(
                                { remarks: e.target.value },
                                index
                              )
                            }
                            className="w-full"
                          />
                        </div>
                      </div>
                    )}
                  </div>
                ))}
              </div>
            </div>

            {/* Add Another Chemical button */}
            <div className="flex justify-start mt-4">
              <Button
                type="button"
                variant="outline"
                onClick={() => addNewChemical()}
                className="flex items-center border-[#294d48] hover:bg-[#294d48]/5"
              >
                <Plus className="h-4 w-4 mr-1 text-[#294d48] font-bold stroke-[2.5px]" />
                <span className="bg-gradient-to-r from-[#294d48] via-[#294d48] to-[#294d48]/80 bg-clip-text text-transparent font-medium">
                  Add Another Chemical
                </span>
              </Button>
            </div>
          </div>

          {/* Create Enquiry button */}
          <Button
            type="button"
            onClick={handleFormSubmit}
            disabled={isSubmitting || selectedCategory === ""}
            className="w-full text-gradient text-white py-2 rounded-md"
          >
            {isSubmitting ? "Creating..." : "Create Enquiry"}
          </Button>
          
          {/* Show message if user doesn't have category */}
          {selectedCategory === "" && (
            <div className="mt-2 text-center">
              <p className="text-red-500 text-sm font-medium">
                Please select a category to continue.
              </p>
            </div>
          )}
        </div>
      )}
      <DocumentUploadModal
        visible={showDocModal}
        onClose={() => setShowDocModal(false)}
        filesByType={filesByType}
        onFilesChange={handleFilesChange}
      />

      {/* Success Dialog */}
      <EnquirySuccessDialog
        showDialog={showSuccessDialog}
        setShowDialog={setShowSuccessDialog}
      />
    </div>
  );
};

export default NewCreateEnquiryForm;
