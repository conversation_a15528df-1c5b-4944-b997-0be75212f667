import React, { useState, useEffect } from "react";
import {
  <PERSON>Tex<PERSON>,
  <PERSON>clip,
  Eye,
  Download,
  FileIcon,
  ChevronDown,
  ChevronUp,
} from "lucide-react";
import { Button } from "@/components/ui/button";
import { supabase, STORAGE_BUCKETS } from "@/integrations/supabase/client";
import {
  Dialog,
  DialogContent,
  DialogTitle,
  DialogDescription,
} from "@/components/ui/dialog";
import { ScrollArea } from "@/components/ui/scroll-area";
import { toast } from "sonner";
import { FilePlus } from 'lucide-react';

interface PORaisedItemProps {
  item: any;
  enquiryId: string;
  formatTimestamp: (timestamp: string | null | undefined) => string;
  formatTime: (timestamp: string | null | undefined) => string;
  onRefresh?: () => void; // Optional callback for refreshing parent component
  handleRequestNewQuotation?: () => void; // Callback for requesting new quotation
  showRequestButton?: boolean; // Optional prop to control visibility of request button
  index?: number; // Optional index prop to control button visibility
}

const PORaisedItem: React.FC<PORaisedItemProps> = ({
  item,
  enquiryId,
  formatTimestamp,
  formatTime,
  onRefresh,
  handleRequestNewQuotation,
  showRequestButton,
  index = 0, // Default index to 0 if not provided
}) => {
  const [isDocumentsModalOpen, setIsDocumentsModalOpen] = useState(false);
  const [isExpanded, setIsExpanded] = useState(false);
  const [purchaseOrderDetails, setPurchaseOrderDetails] = useState<any>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [documents, setDocuments] = useState<
    Array<{ id: string; name: string; url: string; file_path: string }>
  >([]);
  const [isLoadingDocuments, setIsLoadingDocuments] = useState(false);

  // Function to handle file download
  const handleDownload = async (file: { id: string; name: string; file_path: string }) => {
    try {
      const { data, error } = await supabase.storage
        .from(STORAGE_BUCKETS.PURCHASE_ORDER_DOCUMENTS)
        .download(file.file_path);

      if (error) {
        console.error('Download error:', error);
        toast.error('Failed to download file');
        return;
      }

      // Create a blob URL and trigger download
      const blob = new Blob([data], { type: data.type });
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = file.name;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      window.URL.revokeObjectURL(url);

      toast.success('File downloaded successfully');
    } catch (error) {
      console.error('Download error:', error);
      toast.error('Failed to download file');
    }
  };

  // Function to fetch PO documents
  const fetchPODocuments = async (poId: string) => {
    if (!poId) return;

    setIsLoadingDocuments(true);

    try {
      const { data, error } = await supabase
        .from("purchase_order_attachments")
        .select("id, file_name, file_path")
        .eq("purchase_order_id", poId);

      if (error) {
        console.error("Error fetching PO documents:", error);
        toast.error("Failed to load PO documents");
        return;
      }

      if (!data || data.length === 0) {
        setDocuments([]);
        return;
      }

      const documentsWithUrls = data.map((item) => ({
        id: item.id,
        name: item.file_name,
        url:
          supabase.storage
            .from(STORAGE_BUCKETS.PURCHASE_ORDER_DOCUMENTS)
            .getPublicUrl(item.file_path).data?.publicUrl || "",
        file_path: item.file_path,
      }));

      setDocuments(documentsWithUrls);
    } catch (error) {
      console.error("Error in fetchPODocuments:", error);
      toast.error("Failed to load PO documents");
    } finally {
      setIsLoadingDocuments(false);
    }
  };

  useEffect(() => {
    const fetchPurchaseOrderDetails = async () => {
      try {
        setIsLoading(true);

        // Fetch purchase order details
        const { data, error } = await supabase
          .from("purchase_orders")
          .select("*")
          .eq("enquiry_id", enquiryId)
          .eq("history_status_id", item.id)
          .single();

        if (error) {
          console.error("Error fetching purchase order details:", error);
          return;
        }

        setPurchaseOrderDetails(data);

        // Fetch documents for this PO
        if (data && data.id) {
          fetchPODocuments(data.id);
        }
      } catch (error) {
        console.error("Error in fetchPurchaseOrderDetails:", error);
      } finally {
        setIsLoading(false);
      }
    };

    if (enquiryId && item.id) {
      fetchPurchaseOrderDetails();
    }
  }, []);

  return (
    <div className="flex gap-4">
      <div className="flex-none">
        <div className="w-8 h-8 rounded-full bg-green-100 flex items-center justify-center">
          <FileText className="h-4 w-4 text-green-600" />
        </div>
      </div>
      <div className="flex-grow">
        <div className="flex justify-between">
          <h2 className="text-base font-bold text-700">
            Purchase Order Raised
          </h2>
          <span className="text-sm text-gray-500">
            {formatTimestamp(item.created_at)} {formatTime(item.created_at)}
          </span>
        </div>

        {isLoading ? (
          <p className="text-sm text-gray-500 mt-2">
            Loading purchase order details...
          </p>
        ) : purchaseOrderDetails ? (
          <div className="mt-2 space-y-2">
            <div className="flex items-center mt-1">
              <Button
                variant="outline"
                size="sm"
                className="flex items-center justify-between text-sm font-semibold text-gray-600 hover:text-gray-900 py-2 px-3 border border-gray-200 rounded-md bg-white hover:bg-gray-50 transition-colors"
                onClick={() => setIsExpanded(!isExpanded)}
              >
                <span>PO Details</span>
                {isExpanded ? (
                  <ChevronUp className="h-4 w-4 ml-2" />
                ) : (
                  <ChevronDown className="h-4 w-4 ml-2" />
                )}
              </Button>
              {showRequestButton && index === 0 && (
                <span className="ml-8">
                  <Button
                    variant="outline"
                    size="sm"
                    className="h-9 text-xs font-medium flex items-center gap-1.5 bg-white"
                    onClick={() => handleRequestNewQuotation()}
                  >
                    <FilePlus className="h-3.5 w-3.5" />
                    Request for New Quotation
                  </Button>
                </span>
              )}
            </div>

            {isExpanded && (
              <div className="mt-3 bg-gray-50 rounded-lg border border-gray-200 overflow-hidden mb-4">
                {/* Table Container with horizontal scroll */}
                <div className="overflow-x-auto">
                  <table className="w-full text-sm border border-gray-200">
                    <thead className="bg-gray-100">
                      <tr>
                        <th className="p-2 text-left font-medium border-r">PO Number</th>
                        <th className="p-2 text-left font-medium border-r">Frequency Days</th>
                        <th className="p-2 text-left font-medium border-r">Remarks</th>
                        <th className="p-2 text-left font-medium">Attachments</th>
                      </tr>
                    </thead>
                    <tbody>
                      <tr>
                        <td className="p-2 border-r border-t bg-gray-50">
                          {purchaseOrderDetails.po_number || '-'}
                        </td>
                        <td className="p-2 border-r border-t bg-gray-50">
                          {purchaseOrderDetails.frequency || '-'}
                        </td>
                        <td className="p-2 border-r border-t bg-gray-50">
                          {purchaseOrderDetails.notes || '-'}
                        </td>
                        <td className="p-2 border-t bg-gray-50">
                          <button
                            className="flex items-center gap-1.5 text-gray-600 hover:text-gray-900 px-2 py-1 text-sm bg-gray-100 hover:bg-gray-200 rounded-md"
                            onClick={() => setIsDocumentsModalOpen(true)}
                          >
                            <Paperclip className="h-3.5 w-3.5" />
                            <span>View</span>
                          </button>
                        </td>
                      </tr>
                    </tbody>
                  </table>
                </div>
              </div>
            )}
          </div>
        ) : (
          <p className="text-sm text-gray-500 mt-2">
            No purchase order details found.
          </p>
        )}

        {/* Notes from status history if available */}
        {item.notes && !isLoading && !purchaseOrderDetails && (
          <p className="text-sm text-gray-600 mt-1">{item.notes}</p>
        )}

        {/* Documents Modal */}
        <Dialog
          open={isDocumentsModalOpen}
          onOpenChange={setIsDocumentsModalOpen}
        >
          <DialogContent className="max-w-md">
            <div className="flex justify-between items-center mb-4">
              <DialogTitle>Purchase Order Documents</DialogTitle>
            </div>

            <DialogDescription className="text-sm text-gray-500">
              Click "Download" to save the file.
            </DialogDescription>

            <ScrollArea className="mt-4 max-h-[300px]">
              {isLoadingDocuments && (
                <p className="text-sm text-center py-2">Loading documents...</p>
              )}

              {!isLoadingDocuments && documents.length > 0
                ? documents.map((file) => (
                    <div
                      key={file.id}
                      className="flex justify-between items-center p-3 bg-gray-100 rounded-md mb-2"
                    >
                      <div className="text-sm truncate flex-1 flex items-center">
                        <FileIcon className="w-4 h-4 mr-2" />
                        {file.name}
                      </div>
                      <div className="flex space-x-2">
                        {/* <a
                          href={file.url}
                          target="_blank"
                          rel="noopener noreferrer"
                        >
                          <Button variant="outline" size="sm">
                            <Eye className="w-4 h-4 mr-1" /> View
                          </Button>
                        </a> */}
                        <Button 
                          variant="outline" 
                          size="sm"
                          onClick={() => handleDownload(file)}
                        >
                          <Download className="w-4 h-4 mr-1" /> Download
                        </Button>
                      </div>
                    </div>
                  ))
                : !isLoadingDocuments && (
                    <p className="text-sm text-center py-2 text-gray-500">
                      No documents found
                    </p>
                  )}
            </ScrollArea>
          </DialogContent>
        </Dialog>
      </div>
    </div>
  );
};

export default PORaisedItem;
