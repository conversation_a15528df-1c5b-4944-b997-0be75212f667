import { Modal, Select, Input } from "antd";
import { useEffect, useMemo, useState } from "react";
import { Button } from "@/components/ui/button";
import { X, Upload, FileText, Plus, Trash2 } from "lucide-react";
import { supabase } from "@/integrations/supabase/client";
import { toast } from "sonner";
import { Label } from "recharts";
import { replace, useLocation, useNavigate } from "react-router-dom";
import { createMeetingSummary } from "@/lib/utils";

interface CreatePurchaseOrderModalProps {
  open: boolean;
  onClose: () => void;
  onSuccess?: () => void;
}

type CustomerOption = {
  value: string;
  label: string;
};

type EnquiryOption = {
  value: string;
  label: string | JSX.Element;
  chemical_name?: string;
  quantity?: number;
  quantity_unit?: string;
  procurement_volume?: number;
  procurement_unit?: string;
  id?: string;
  enquiry_id?: string;
};

type ChemicalEntry = {
  enquiryId: string;
  enquiryNumber: string;
  chemicalName: string;
  orderValue: string;
  orderValueUnit: string;
  poFreqDays: string;
  additionalDocs: File[];
  remarks: string;
};

type POFormData = {
  // PO Level fields
  customerId: string;
  customerName: string;
  poDocument: File[];
  poNumber: string;
  
  // Chemical Level entries
  chemicalEntries: ChemicalEntry[];
};

const CreatePurchaseOrderModal = ({
  open,
  onClose,
  onSuccess,
}: CreatePurchaseOrderModalProps) => {
  const [enquiryOptions, setEnquiryOptions] = useState<EnquiryOption[]>([]);
  const [isLoadingEnquiries, setIsLoadingEnquiries] = useState(false);
  const [isLoadingSuggestions, setIsLoadingSuggestions] = useState(false);

  const customerName = localStorage.getItem('customerName');
  const customerId = localStorage.getItem("customerId")
  const location = useLocation();
  const queryParams = new URLSearchParams(location.search);
  const navigate = useNavigate();


  const [isSubmitting, setIsSubmitting] = useState(false);
  
  // chemical level fields
  const [formData, setFormData] = useState<POFormData>({
    customerId: "",
    customerName:customerName,
    poDocument: [],
    poNumber: "",
    chemicalEntries: [
      {
        enquiryId: "",
        enquiryNumber: "",
        chemicalName: "",
        orderValue: "",
        orderValueUnit: "USD",
        poFreqDays: "",
        additionalDocs: [],
        remarks: "",
      },
    ],
  });


  useEffect(()=>{
    if(customerName){
      handleSelectCustomer(customerId,customerName,0)
    }
  },[])

  // State to track validation errors
  const [validationErrors, setValidationErrors] = useState<{
    poLevel?: {
      customerId?: boolean;
      poDocument?: boolean;
      poNumber?: boolean;
    };
    chemicalEntries?: {
      [key: number]: {
        enquiryId?: boolean;
        orderValue?: boolean;
        poFreqDays?: boolean;
      };
    };
  }>({});

  // State to track customer suggestions
  const [customerSuggestions, setCustomerSuggestions] = useState<CustomerOption[]>([]);
  const [showSuggestions, setShowSuggestions] = useState(false);

  // Reset form data
  const resetForm = () => {
    console.log("Resetting form");
    setEnquiryOptions([]);
    setCustomerSuggestions([]);
    setFormData({
      customerId: "",
      customerName: "",
      poDocument: [],
      poNumber: "",
      chemicalEntries: [
        {
          enquiryId: "",
          enquiryNumber: "",
          chemicalName: "",
          orderValue: "",
          orderValueUnit: "USD",
          poFreqDays: "",
          additionalDocs: [],
          remarks: "",
        },
      ],
    });
    setValidationErrors({});
  };

  // Derived: Total PO value across all chemical entries
  const totalPOValue = useMemo(() => {
    try {
      const sum = formData.chemicalEntries.reduce((acc, entry) => {
        const numeric = parseFloat(entry.orderValue || "0");
        return acc + (isNaN(numeric) ? 0 : numeric);
      }, 0);
      return Number.isFinite(sum) ? sum : 0;
    } catch (e) {
      return 0;
    }
  }, [formData.chemicalEntries]);

  // Function to add a new chemical entry
  const addChemicalEntry = () => {
    setFormData(prev => ({
      ...prev,
      chemicalEntries: [
        ...prev.chemicalEntries,
        {
          enquiryId: "",
          enquiryNumber: "",
          chemicalName: "",
          orderValue: "",
          orderValueUnit: prev.chemicalEntries[0].orderValueUnit,
          poFreqDays: "",
          additionalDocs: [],
          remarks: "",
        },
      ],
    }));
  };

  // Function to remove a chemical entry
  const removeChemicalEntry = (index: number) => {
    if (formData.chemicalEntries.length <= 1) {
      toast.error("At least one chemical entry is required.");
      return;
    }

    setFormData(prev => ({
      ...prev,
      chemicalEntries: prev.chemicalEntries.filter((_, i) => i !== index),
    }));
  };

  // Function to update PO level fields
  const updatePOField = (field: keyof Omit<POFormData, 'chemicalEntries'>, value: string | File[]) => {
    setFormData(prev => ({
      ...prev,
      [field]: value,
    }));
  };

  // Function to update chemical entry fields
  const updateChemicalEntry = (index: number, field: keyof ChemicalEntry, value: string | File[]) => {
    setFormData(prev => ({
      ...prev,
      chemicalEntries: prev.chemicalEntries.map((entry, i) =>
        i === index ? { ...entry, [field]: value } : entry
      ),
    }));
  };

  // Function to fetch customers based on search term
  const fetchCustomers = async (searchTerm: string) => {
    setIsLoadingSuggestions(true);
    try {
      const { data: customersData, error } = await supabase
        .from("customer")
        .select("id, customer_full_name")
        .textSearch("customer_full_name", `${searchTerm}:*`, { type: "websearch" })
        .limit(10);

      if (error) {
        console.error("Error fetching customers:", error);
        return [];
      }

      const customerOptions: CustomerOption[] = customersData.map(
        (customer) => ({
          value: customer.id,
          label: customer.customer_full_name,
        })
      );

      return customerOptions;
    } catch (error) {
      console.error("Error fetching customers:", error);
      return [];
    } finally {
      setIsLoadingSuggestions(false);
    }
  };

  // Function to handle customer input and show suggestions
  const handleCustomerInput = async (value: string) => {
    updatePOField('customerName', value);
    updatePOField('customerId', ''); // Reset ID until a suggestion is selected

    if (value.length >= 1) {
      const customerOptions = await fetchCustomers(value);
      setCustomerSuggestions(customerOptions);
      setShowSuggestions(customerOptions.length > 0);
    } else {
      setShowSuggestions(false);
      setCustomerSuggestions([]);
    }
  };

  // Function to handle customer suggestion selection
  const handleSelectCustomer = (customerId: string, customerName: string) => {
    updatePOField('customerId', customerId);
    updatePOField('customerName', customerName);
    setShowSuggestions(false);

    // Fetch enquiries for the selected customer
    if (customerId) {
      fetchEnquiriesForCustomer(customerId);
    }
  };

  // Function to fetch enquiries for a specific customer
  const fetchEnquiriesForCustomer = async (customerId: string) => {
    if (!customerId) {
      setEnquiryOptions([]);
      return;
    }

    setIsLoadingEnquiries(true);
    try {
      const { data: enquiries, error } = await supabase
        .from("enquiries")
        .select(
          "id, chemical_name, quantity, quantity_unit, procurement_volume, procurement_unit, enquiry_id"
        )
        .eq("customer_id", customerId)
        .not(
          "current_status",
          "in",
          "(quote_rejected,sample_rejected,cancelled,regret)"
        );

      if (error) {
        console.error("Error fetching enquiries:", error);
        toast.error("Failed to load enquiries");
        setEnquiryOptions([]);
        return;
      }

      const options = enquiries.map((enquiry) => ({
        value: enquiry.id,
        label: enquiry.chemical_name,
        chemical_name: enquiry.chemical_name,
        quantity: enquiry.quantity,
        quantity_unit: enquiry.quantity_unit,
        procurement_volume: enquiry.procurement_volume,
        procurement_unit: enquiry.procurement_unit,
        id: enquiry.id,
        enquiry_id: enquiry.enquiry_id,
      }));

      setEnquiryOptions(options);
    } catch (error) {
      console.error("Error fetching enquiries:", error);
      toast.error("Failed to load enquiries");
      setEnquiryOptions([]);
    } finally {
      setIsLoadingEnquiries(false);
    }
  };

  // Function to fetch PO Frequency from Account Planning for a specific chemical
  const fetchPOFrequencyFromAccountPlanning = async (customerId: string, chemicalName: string) => {
    try {
      const { data, error } = await supabase
        .from('account_planning')
        .select('po_frequency')
        .eq('customer_id', customerId)
        .eq('chemical_name', chemicalName)
        .order('updated_at', { ascending: false })
        .limit(1);

      if (error) {
        console.error('Error fetching PO frequency from account planning:', error);
        return null;
      }

      return data && data.length > 0 ? data[0].po_frequency : null;
    } catch (error) {
      console.error('Error fetching PO frequency from account planning:', error);
      return null;
    }
  };

  // Function to update Account Planning with new PO Frequency
  const updateAccountPlanningPOFrequency = async (customerId: string, chemicalName: string, poFrequency: number) => {
    try {
      const { error } = await supabase
        .from('account_planning')
        .update({
          po_frequency: poFrequency,
          updated_at: new Date().toISOString()
        })
        .eq('customer_id', customerId)
        .eq('chemical_name', chemicalName);

      if (error) {
        console.error('Error updating account planning PO frequency:', error);
        return false;
      }

      return true;
    } catch (error) {
      console.error('Error updating account planning PO frequency:', error);
      return false;
    }
  };

  // Validation function
  const validateForm = (): boolean => {
    const errors: typeof validationErrors = {
      poLevel: {},
      chemicalEntries: {},
    };

    let hasErrors = false;

    // Validate PO level fields
    if (!formData.customerId && !formData.customerName) {
      errors.poLevel!.customerId = true;
      hasErrors = true;
    }

    if (!formData.poNumber) {
      errors.poLevel!.poNumber = true;
      hasErrors = true;
    }

    if (!formData.poDocument || formData.poDocument.length === 0) {
      errors.poLevel!.poDocument = true;
      hasErrors = true;
    }

    // Validate chemical entries
    formData.chemicalEntries.forEach((entry, index) => {
      errors.chemicalEntries![index] = {};

      if (!entry.enquiryId) {
        errors.chemicalEntries![index].enquiryId = true;
        hasErrors = true;
      }

      if (!entry.orderValue || parseFloat(entry.orderValue) <= 0) {
        errors.chemicalEntries![index].orderValue = true;
        hasErrors = true;
      }

      if (!entry.poFreqDays || parseInt(entry.poFreqDays) <= 0) {
        errors.chemicalEntries![index].poFreqDays = true;
        hasErrors = true;
      }
    });

    setValidationErrors(errors);
    return !hasErrors;
  };

  // Submit function
  const submitForm = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) {
      toast.error("Please fill in all required fields");
      return;
    }

    setIsSubmitting(true);

    try {
      // Get the current user session
      const { data: authData } = await supabase.auth.getSession();
      if (!authData.session) {
        throw new Error("User must be authenticated to submit purchase order");
      }

      // Handle customer creation if needed
      let customerId = formData.customerId;
      if (!customerId && formData.customerName) {
        // Check if customer exists by name
        const { data: existingCustomers, error: customerLookupError } = await supabase
          .from("customer")
          .select("id")
          .ilike("customer_full_name", formData.customerName)
          .limit(1);

        if (customerLookupError) {
          console.error("Error looking up customer:", customerLookupError);
        } else if (existingCustomers && existingCustomers.length > 0) {
          customerId = existingCustomers[0].id;
        } else {
          // Create new customer
          const { data: newCustomer, error: createCustomerError } = await supabase
            .from("customer")
            .insert({
              customer_full_name: formData.customerName,
              created_by: authData.session.user.id,
            })
            .select("id")
            .single();

          if (createCustomerError) {
            throw new Error(`Failed to create new customer: ${createCustomerError.message}`);
          }

          if (newCustomer) {
            customerId = newCustomer.id;
          }
        }
      }

      // Process enquiry status updates for all chemical entries first
      for (const chemicalEntry of formData.chemicalEntries) {
        const enquiryId = chemicalEntry.enquiryId;

        // Handle enquiry status updates (same logic as original)
        const { data: enquiryData, error: enquiryError } = await supabase
          .from("enquiries")
          .select("current_status")
          .eq("id", enquiryId)
          .single();

        if (enquiryError) {
          throw new Error(`Failed to fetch enquiry data: ${enquiryError.message}`);
        }

        // Handle quote acceptance if needed
        if (enquiryData.current_status === "pricing_quotation_generated") {

          const { data: pricingQuoteId, error: pricingQuoteError } = await supabase
            .from("enquiry_status_history")
            .select("id")
            .eq("enquiry_id", enquiryId)
            .eq("status", "pricing_quotation_generated")
            .order("created_at", { ascending: false })
            .limit(1)
            .single();

          if (pricingQuoteError) {
            throw new Error(`Error fetching pricing quote id: ${pricingQuoteError.message}`);
          }

          const { error: feedbackError } = await supabase
            .from("quotation_feedback")
            .insert({
              enquiry_id: enquiryId,
              pricing_quote_id: pricingQuoteId.id,
              response: "quote_accepted",
              remarks: "Accepted through direct PO",
              reason: "Direct PO",
              submitted_by: authData.session.user.email,
            });

          if (feedbackError) {
            throw new Error(`Error submitting feedback: ${feedbackError.message}`);
          }
        }

        // Handle sample acceptance if needed
        if (enquiryData.current_status === "sample_delivered") {

          const { data: feedbackHistoryId, error: feedbackHistoryError } = await supabase
            .from("enquiry_status_history")
            .select("id")
            .eq("enquiry_id", enquiryId)
            .eq("status", "sample_delivered")
            .order("created_at", { ascending: false })
            .limit(1)
            .single();

          if (feedbackHistoryError) {
            throw new Error(`Error fetching feedback history: ${feedbackHistoryError.message}`);
          }

          const { error: sampleFeedbackError } = await supabase
            .from("sample_feedback")
            .insert({
              enquiry_id: enquiryId,
              feedback_history_id: feedbackHistoryId.id,
              response: "sample_accepted",
              remarks: "Accepted through direct PO",
              reason: "Direct PO",
              submitted_by: authData.session.user.email,
              type: "sample",
            });

          if (sampleFeedbackError) {
            throw new Error(`Error submitting sample feedback: ${sampleFeedbackError.message}`);
          }
        }

        // Update enquiry status to po_raised
        const { error: updateEnquiryError } = await supabase
          .from("enquiries")
          .update({
            current_status: "po_raised",
            last_status_change: new Date().toISOString(),
          })
          .eq("id", enquiryId);

        if (updateEnquiryError) {
          throw new Error(`Failed to update enquiry status: ${updateEnquiryError.message}`);
        }

        // Create status history record
        // const { error: historyError } = await supabase
        //   .from("enquiry_status_history")
        //   .insert({
        //     enquiry_id: enquiryId,
        //     status: "po_raised",
        //     changed_by: authData.session.user.email,
        //     sales_agent_email: authData.session.user.email,
        //     notes: "Purchase order raised",
        //   });

        // if (historyError) {
        //   throw new Error(`Failed to create status history: ${historyError.message}`);
        // }
      }

      // Create ONE purchase order for all chemicals (using the first enquiry for the RPC call)
      const firstEnquiryId = formData.chemicalEntries[0].enquiryId;

      // Get the latest status history for the first enquiry to get history_status_id
      // const { data: latestHistory, error: historyFetchError } = await supabase
      //   .from("enquiry_status_history")
      //   .select("id")
      //   .eq("enquiry_id", firstEnquiryId)
      //   .eq("status", "po_raised")
      //   .order("created_at", { ascending: false })
      //   .limit(1)
      //   .single();

      // if (historyFetchError) {
      //   throw new Error(`Failed to fetch status history: ${historyFetchError.message}`);
      // }

      const { data: poData, error: poError } = await supabase.rpc(
        "create_purchase_order",
        {
          p_enquiry_id: null,
          p_notes: `PO for ${formData.chemicalEntries.length} chemicals`,
          p_created_by: authData.session.user.id,
          p_po_number: formData.poNumber,
          p_frequency: null, // PO-level frequency not applicable for multi-chemical POs
          p_customer_id: customerId,
          p_history_status_id: null,
          p_po_value: totalPOValue.toString(),
          p_po_currency: formData.chemicalEntries[0]?.orderValueUnit || null,
        }
      );

      if (poError) {
        throw new Error(`Failed to create purchase order: ${poError.message}`);
      }

          if (!poData) {
            throw new Error("Failed to create purchase order: No ID returned");
          }

      console.log("Raw PO data returned:", poData, "Type:", typeof poData);

      // The RPC function returns an object with id property
      const purchaseOrderId = (poData as { id: string }).id;

      console.log("Extracted purchaseOrderId:", purchaseOrderId);

      if (!purchaseOrderId || purchaseOrderId === "null" || purchaseOrderId === "undefined") {
        throw new Error("Failed to extract valid purchase order ID");
      }

      // Upload PO document (shared across all chemical entries)
      if (formData.poDocument && formData.poDocument.length > 0) {
        for (const file of formData.poDocument) {
          const fileExt = file.name.split(".").pop();
          const filePath = `${purchaseOrderId}/${crypto.randomUUID()}.${fileExt}`;

          const { error: uploadError } = await supabase.storage
            .from("purchase_order_documents")
            .upload(filePath, file, {
              cacheControl: "3600",
              upsert: false,
            });

          if (uploadError) {
            console.error("Error uploading file to storage:", uploadError);
            toast.error(`Failed to upload ${file.name}: ${uploadError.message}`);
            continue;
          }

          const { error: docError } = await supabase
            .from("purchase_order_attachments")
            .insert({
              purchase_order_id: purchaseOrderId,
              file_name: file.name,
              file_path: filePath,
              content_type: file.type,
              doc_type: "PO_FILE",
            });

          if (docError) {
            console.error("Error creating document record:", docError);
            toast.error(`Failed to record document ${file.name}: ${docError.message}`);
          }
        }
      }

      // Now create purchase_order_items records for each chemical entry
      for (const chemicalEntry of formData.chemicalEntries) {
        // First create purchase_order_items record and get its ID
        // console.log("Creating purchase_order_items with:", {
        //   purchase_order_id: purchaseOrderId,
        //   enquiry_id: chemicalEntry.enquiryId,
        //   chemical_name: chemicalEntry.chemicalName,
        //   order_value: parseFloat(chemicalEntry.orderValue),
        //   order_value_unit: chemicalEntry.orderValueUnit,
        //   po_freq_days: parseInt(chemicalEntry.poFreqDays),
        //   remarks: chemicalEntry.remarks,
        // });

        const { data: historyData, error: historyError } = await supabase
        .from("enquiry_status_history")
        .insert({
          enquiry_id: chemicalEntry.enquiryId,
          status: "po_raised",
          changed_by: authData.session.user.email,
          sales_agent_email: authData.session.user.email,
          notes: "Purchase order raised",
        })
        .select("id")
        .single();

      if (historyError) {
        throw new Error(`Failed to create status history: ${historyError.message}`);
      }

        const { data: itemData, error: itemError } = await supabase
          .from("purchase_order_items")
          .insert({
            purchase_order_id: purchaseOrderId,
            enquiry_id: chemicalEntry.enquiryId,
            chemical_name: chemicalEntry.chemicalName,
            po_value: parseFloat(chemicalEntry.orderValue),
            po_currency: chemicalEntry.orderValueUnit,
            frequency: parseInt(chemicalEntry.poFreqDays),
            remarks: chemicalEntry.remarks,
            history_status_id: historyData?.id
            
          })
          .select("id")
          .single();

        if (itemError) {
          console.error("Error creating purchase order item:", itemError);
          toast.error(`Failed to create purchase order item: ${itemError.message}`);
          continue; // Skip this chemical entry
        }

      
          const meetingId = localStorage.getItem("meetingId");

          const payload = {
            meeting_id: meetingId,
            enquiry_id: chemicalEntry.enquiryId,
            summary_action_type: "po_creation",
            ref_document_id: itemData?.id,
          }

          if (meetingId){
            await createMeetingSummary(payload ,false)
          }

        const purchaseOrderItemId = itemData.id;
        console.log("Successfully created purchase_order_items record with ID:", purchaseOrderItemId);

        // Upload additional documents for this chemical entry
        if (chemicalEntry.additionalDocs && chemicalEntry.additionalDocs.length > 0) {
          for (const file of chemicalEntry.additionalDocs) {
            const fileExt = file.name.split(".").pop();
            const filePath = `${purchaseOrderId}/${crypto.randomUUID()}.${fileExt}`;

            const { error: uploadError } = await supabase.storage
              .from("purchase_order_documents")
              .upload(filePath, file, {
                cacheControl: "3600",
                upsert: false,
              });

            if (uploadError) {
              console.error("Error uploading additional file:", uploadError);
              toast.error(`Failed to upload ${file.name}: ${uploadError.message}`);
              continue;
            }

            // Save to purchase_order_chemical_level_docs table
            const { error: docError } = await supabase
              .from("purchase_order_items_attachments")
              .insert({
                purchase_order_id: purchaseOrderId,
                purchase_order_items_id: purchaseOrderItemId,
                file_name: file.name,
                file_path: filePath,
                content_type: file.type,
                doc_type: "Additional_Docs",
                size: file.size,
              });

            if (docError) {
              console.error("Error creating chemical document record:", docError);
              toast.error(`Failed to record document ${file.name}: ${docError.message}`);
            } else {
              console.log("Successfully created chemical document record for:", file.name);
            }
          }
        }
        if (chemicalEntry.poFreqDays && parseInt(chemicalEntry.poFreqDays) >= 0 && formData.customerId) {
          const success = await updateAccountPlanningPOFrequency(
            formData.customerId,
            chemicalEntry.chemicalName,
            parseInt(chemicalEntry.poFreqDays)
          );
        
          if (success) {
            console.log(`Account Planning updated for ${chemicalEntry.chemicalName}`);
          } else {
            console.warn(`Failed to update Account Planning for ${chemicalEntry.chemicalName}`);
          }
        }
      }


      // Reset form after successful submission
      resetForm();
      toast.success("Purchase order submitted successfully!");

      if (onSuccess) {
        onSuccess();
      }

      onClose();
    } catch (error) {
      console.error("Error submitting purchase order:", error);
      toast.error("Failed to submit purchase order. Please try again.");
    } finally {
      navigate(location.pathname, { replace: true });
      sessionStorage.removeItem("isOpenPoForm")
      localStorage.removeItem("meetingId")
      localStorage.removeItem("customerName")
      localStorage.removeItem("customerId")
      setIsSubmitting(false);
    }
  };

  return (
    <Modal
      open={open}
      onCancel={() => {
        resetForm();
        onClose();
      }}
      title="Upload Purchase Order"
      footer={null}
      width="90%"
      centered
      destroyOnClose={true}
      getContainer={document.body}
      closeIcon={<X className="h-4 w-4" />}
    >
      <form onSubmit={submitForm} className="space-y-6">
        {/* PO Level Fields */}
        <div className="bg-gray-50 p-4 rounded-lg">
          <h3 className="text-lg font-medium mb-4">Purchase Order Details</h3>
          <div className="grid grid-cols-3 gap-4">
            {/* Customer */}
            <div className="relative">
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Customer <span className="text-red-500">*</span>
              </label>
              <Input
                type="text"
                value={formData.customerName}
                onChange={(e) => handleCustomerInput(e.target.value)}
                placeholder="Enter customer name"
                className={`w-full h-8${
                  validationErrors.poLevel?.customerId ? "border-red-500" : ""
                }`}
              />
              
              {/* Customer Suggestions */}
              {showSuggestions && (
                <div className="absolute z-10 mt-1 w-full bg-white border border-gray-200 rounded-md shadow-lg max-h-60 overflow-auto">
                  {isLoadingSuggestions ? (
                    <div className="px-4 py-2 text-center text-gray-500">Loading...</div>
                  ) : customerSuggestions.length > 0 ? (
                    customerSuggestions.map((customer) => (
                      <div
                        key={customer.value}
                        className="px-4 py-2 hover:bg-gray-100 cursor-pointer"
                        onClick={() => handleSelectCustomer(customer.value, customer.label)}
                      >
                        {customer.label}
                      </div>
                    ))
                  ) : (
                    <div className="px-4 py-2 text-center text-gray-500">
                      No customers found. Type to create a new one.
                    </div>
                  )}
                </div>
              )}
            </div>

            {/* PO Number */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                PO Number <span className="text-red-500">*</span>
              </label>
              <Input
                value={formData.poNumber}
                onChange={(e) => updatePOField('poNumber', e.target.value)}
                placeholder="Enter PO number"
                className={`w-full h-8 ${
                  validationErrors.poLevel?.poNumber ? "border-red-500" : ""
                }`}
              />
            </div>

            {/* PO Document */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                PO Document <span className="text-red-500">*</span>
              </label>
              <div
                className={`flex items-center justify-center h-8 px-3 border rounded-md ${
                  validationErrors.poLevel?.poDocument
                    ? "border-red-500"
                    : "border-gray-300"
                } bg-white hover:bg-gray-50 cursor-pointer`}
              >
                <label className="flex items-center justify-center w-full h-full cursor-pointer">
                  <input
                    type="file"
                    className="hidden"
                    accept=".pdf,.doc,.docx,.xls,.xlsx,.jpg,.jpeg,.png"
                    onChange={(e) => {
                      if (e.target.files && e.target.files.length > 0) {
                        updatePOField('poDocument', Array.from(e.target.files));
                      }
                    }}
                  />
                  <div className="flex items-center gap-2 text-sm text-gray-600 w-full">
                    <Upload className="h-4 w-4 flex-shrink-0" />
                    <span className="truncate">
                      {formData.poDocument[0]?.name || "Upload PO"}
                    </span>
                  </div>
                </label>
              </div>
            </div>
          </div>
        </div>

        {/* Chemical Details Section */}
        <div>
          <div className="flex justify-between items-center mb-4">
            <h3 className="text-lg font-medium">Chemical Details</h3>
            <Button
              type="button"
              size="sm"
              onClick={addChemicalEntry}
              className="bg-blue-100 hover:bg-blue-200 text-blue-800 border border-blue-300"
            >
              <Plus className="h-4 w-4 mr-1" />
              Add Chemical
            </Button>
          </div>
          
          {/* Header row */}
          <div className="grid grid-cols-7 gap-3 mb-2 px-2">
            <div className="text-sm font-medium text-gray-700">
              Enquiry <span className="text-red-500">*</span>
            </div>
            <div className="text-sm font-medium text-gray-700">Chemical Name</div>
            <div className="text-sm font-medium text-gray-700">
              Order Value <span className="text-red-500">*</span>
            </div>
            <div className="text-sm font-medium text-gray-700">
              Freq. (Days) <span className="text-red-500">*</span>
            </div>
            <div className="text-sm font-medium text-gray-700">Additional Docs</div>
            <div className="text-sm font-medium text-gray-700">Remarks</div>
          </div>

          {/* Chemical entries */}
          {formData.chemicalEntries.map((entry, index) => (
            <div key={index} className="grid grid-cols-7 gap-3 py-3 px-2 border-t border-gray-200 bg-white rounded-lg mb-2">
              {/* Enquiry Selection */}
              <div>
                <Select
                  value={entry.enquiryId}
                  disabled={!formData.customerId && !customerName}
                  optionLabelProp="label"
                  className={`w-full h-8 ${
                    validationErrors.chemicalEntries?.[index]?.enquiryId ? "border-red-500" : ""
                  }`}
                  onChange={(value) => {
                    const selectedEnquiry = enquiryOptions.find(e => e.value === value);
                    updateChemicalEntry(index, 'enquiryId', value);
                    updateChemicalEntry(index, 'enquiryNumber', selectedEnquiry?.enquiry_id || '');
                    updateChemicalEntry(index, 'chemicalName', selectedEnquiry?.chemical_name || '');
                    updateChemicalEntry(index, "poFreqDays", "");
                    
                    // Auto-fill PO Frequency from Account Planning
                    if (selectedEnquiry?.chemical_name && formData.customerId) {
                      fetchPOFrequencyFromAccountPlanning(formData.customerId, selectedEnquiry.chemical_name)
                        .then((poFreq) => {
                          if (poFreq) {
                            updateChemicalEntry(index, 'poFreqDays', poFreq.toString());
                          }
                        });
                    }
                  }}
                  placeholder="Select Enquiry"
                  showSearch
                  filterOption={(input, option) => {
                    const enquiry = enquiryOptions.find(e => e.value === option?.value);
                    return enquiry?.enquiry_id?.toLowerCase().includes(input.toLowerCase()) || false;
                  }}
                >
                  {enquiryOptions.map((enquiry) => (
                    <Select.Option key={enquiry.value} value={enquiry.value} label={enquiry.enquiry_id}>
                      <div>
                        <div className="font-medium">{enquiry.enquiry_id}</div>
                        <div className="text-sm text-gray-500">{enquiry.chemical_name}</div>
                      </div>
                    </Select.Option>
                  ))}
                </Select>
              </div>

              {/* Chemical Name (Auto-populated, read-only) */}
              <div>
                <Input
                  value={entry.chemicalName}
                  placeholder="Auto-populated"
                  disabled
                  className="w-full bg-gray-50 h-8"
                />
              </div>

              {/* Order Value */}
              <div>
                <div className="text-xs text-gray-500 "> 
                </div>
                <div className="flex">
                  <Input
                    value={entry.orderValue}
                    onChange={(e) => updateChemicalEntry(index, 'orderValue', e.target.value)}
                    placeholder="Value"
                    className={`w-[60%] rounded-r-none h-8 ${
                      validationErrors.chemicalEntries?.[index]?.orderValue ? "border-red-500" : ""
                    }`}
                    type="number"
                    min="0"
                    step="0.01"
                  />
                  <Select
                    value={entry.orderValueUnit}
                    onChange={(value) => {
                      setFormData(prev => ({
                        ...prev,
                        chemicalEntries: prev.chemicalEntries.map((entry, i) =>
                          i === index ? { ...entry, ['orderValueUnit']: value } : { ...entry, ['orderValueUnit']: value }
                        ),
                      }));
                    }}
                    disabled={index != 0}
                    className="w-[45%]"
                    options={[
                      { value: "USD", label: "USD $" },
                      { value: "INR", label: "INR ₹" },
                      { value: "EURO", label: "EUR €" },
                      { value: "YUAN", label: "CNY ¥" },
                      { value: "YEN", label: "JPY ¥" },
                      { value: "AED", label: "AED د.إ" },
                    ]}
                  />
                </div>
              </div>

              {/* PO Frequency Days */}
              <div>
                <div className="text-xs text-gray-500 ">
                </div>
                <Input
                  value={entry.poFreqDays}
                  onChange={async (e) => {
                    const newValue = e.target.value;
                    updateChemicalEntry(index, 'poFreqDays', newValue);
                    
                    // Update Account Planning when PO Frequency changes
                    // if (newValue && parseInt(newValue) > 0 && entry.chemicalName && formData.customerId) {
                    //   const success = await updateAccountPlanningPOFrequency(
                    //     formData.customerId, 
                    //     entry.chemicalName, 
                    //     parseInt(newValue)
                    //   );
                    //   if (success) {
                    //     console.log('Account Planning updated with new PO frequency');
                    //   }
                    // }
                  }}
                  placeholder="Days"
                  className={`w-full h-8 ${
                    validationErrors.chemicalEntries?.[index]?.poFreqDays ? "border-red-500" : ""
                  }`}
                  type="number"
                  min="1"
                  step="1"
                />
              </div>

              {/* Additional Documents */}
              <div>
                <div className="flex items-center justify-center h-8 px-3 border border-gray-300 rounded-md bg-white hover:bg-gray-50 cursor-pointer">
                  <label className="flex items-center justify-center w-full h-full cursor-pointer">
                    <input
                      type="file"
                      className="hidden"
                      accept=".pdf,.doc,.docx,.xls,.xlsx,.jpg,.jpeg,.png"
                      multiple
                      onChange={(e) => {
                        if (e.target.files && e.target.files.length > 0) {
                          updateChemicalEntry(index, 'additionalDocs', Array.from(e.target.files));
                        }
                      }}
                    />
                    <div className="flex items-center gap-2 text-sm text-gray-600 w-full">
                      <FileText className="h-4 w-4 flex-shrink-0" />
                      <span className="truncate">
                        {entry.additionalDocs?.length > 0
                          ? `${entry.additionalDocs.length} files`
                          : "Upload"}
                      </span>
                    </div>
                  </label>
                </div>
              </div>

              {/* Remarks */}
              <div>
                <Input
                  value={entry.remarks}
                  onChange={(e) => updateChemicalEntry(index, 'remarks', e.target.value)}
                  placeholder="Enter remarks"
                  className="w-full"
                />
              </div>

              {/* Delete button */}
              <div className="flex items-center justify-center">
                {formData.chemicalEntries.length > 1 && (
                  <button
                    type="button"
                    onClick={() => removeChemicalEntry(index)}
                    className="text-red-500 hover:text-red-700 flex-shrink-0"
                  >
                    <Trash2 className="h-4 w-4" />
                  </button>
                )}
              </div>
            </div>
          ))}
        </div>

        {/* Footer with buttons */}
        <div className="grid grid-cols-7 gap-3 pt-4 border-t">
        <div className="col-start-3 col-span-1">
        {/* Label on top */}
        <div className="text-sm font-medium text-gray-700 mb-1 block">
          Total PO Value
        </div>

        {/* Input + Select in same row */}
        <div className="flex">
          <Input
          title="Calculated and non editable..."
            value={totalPOValue ? totalPOValue.toString() : ""}
            placeholder="Calculated and non editable..."
            className={`w-[60%] rounded-r-none h-8 ${
              validationErrors.poLevel?.poNumber ? "border-red-500" : ""
            }`}
            disabled
          />
          <Select
            value={formData.chemicalEntries[0].orderValueUnit}
            disabled
            className="w-[50%] rounded-l-none"
            options={[
              { value: "USD", label: "USD $" },
              { value: "INR", label: "INR ₹" },
              { value: "EURO", label: "EUR €" },
              { value: "YUAN", label: "CNY ¥" },
              { value: "YEN", label: "JPY ¥" },
              { value: "AED", label: "AED د.إ" },
            ]}
          />
        </div>
      </div>

                <div className="col-start-6 col-span-2 flex justify-end gap-3">

          <div className="flex gap-3">
            <Button
              type="button"
              variant="outline"
              onClick={() => {
                resetForm();
                onClose();
              }}
              disabled={isSubmitting}
            >
              Cancel
            </Button>
            <Button
              type="submit"
              disabled={isSubmitting}
              className="bg-[#294d48] hover:bg-[#1e3a36] text-white"
            >
              {isSubmitting ? "Submitting..." : "Submit"}
            </Button>
          </div>
        </div>
        </div>
      </form>
    </Modal>
  );
};

export default CreatePurchaseOrderModal;
