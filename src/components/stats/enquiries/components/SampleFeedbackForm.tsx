import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
// FormControls already imports Button, so we don't need to import it here
import CompactDocumentUpload from "@/components/enquiries/components/CompactDocumentUpload";
import { useSampleFeedback } from "../hooks/useSampleFeedback";
import { useEffect, useState } from "react";
import { toast } from "sonner";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { CheckCircle, XCircle, RefreshCw, X } from "lucide-react";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";

type SampleResponse = "sample_accepted" | "sample_rejected" | "sample_redo";

interface SampleFeedbackFormProps {
  sampleDeliveredDate?: string;
  onClose: () => void;
  enquiryId: string;
  onSuccess?: () => void;
  enquiryStatusId?: string;
}

const statusColors = {
  green: "#22c55e",
  blue: "#3b82f6",
  gray: "#6b7280",
};

const SAMPLE_REJECT_REASONS = [
  "Delay in sample",
  "Shipping issue (Label/ Wear tear etc)",
  "TDS/COA mismatch",
  "Issues discovered post testing",
  "Others",
] as const;

const SAMPLE_REDO_REASONS = [
  "Shipping issue (Label/ Wear tear etc)",
  "TDS/COA mismatch",
  "Issues discovered post testing",
  "On hold by customer",
  "Others",
] as const;

const processStages = [
  {
    id: "delivery_confirmation",
    label: "Delivery Confirmation",
    status: { color: "blue", label: "Pending", sublabel: "" },
    details: [
      {
        type: "date",
        label: "Delivery Date",
        value: "",
        required: true,
        labelValue: "deliveryDate",
      },
      {
        type: "number",
        label: "Feedback ETA (Days)",
        value: 30,
        required: true,
        labelValue: "targetQualification",
      },
    ],
  },
  {
    id: "testing_start",
    label: "Testing Start Confirmation",
    status: { color: "blue", label: "Pending", sublabel: "by May 24, 2025" },
    details: [
      {
        type: "textarea",
        label: "Remarks",
        placeholder: "Enter remarks...",
        required: false,
        labelValue: "remarks",
      },
      {
        type: "date",
        label: "Testing Started",
        value: "",
        required: true,
        labelValue: "testingStarted",
      },
    ],
  },
  {
    id: "feedback",
    label: "Feedback",
    status: { color: "gray", label: "Pending", sublabel: "by Jun 8, 2025" },
    details: [
      {
        type: "select",
        label: "Response Type",
        options: ["Accept Sample", "Reject Sample", "Request New Sample"],
        placeholder: "Select a response",
        isResponseField: true,
        required: true,
        labelValue: "responseType",
      },
      {
        type: "select",
        label: "Reason",
        options: SAMPLE_REJECT_REASONS,
        placeholder: "Select a reason",
        isReasonField: true,
        required: true,
        labelValue: "reason",
      },
      {
        type: "textarea",
        label: "Remarks",
        placeholder: "Enter feedback remarks...",
        required: false,
        labelValue: "remarks",
      },
      {
        type: "file",
        label: "Attachment",
        required: false,
        labelValue: "attachment",
      },
    ],
  },
];

const SampleFeedbackForm = ({
  sampleDeliveredDate = "",
  onClose,
  enquiryId,
  onSuccess,
  enquiryStatusId,
}: SampleFeedbackFormProps) => {
  const {
    selectedResponse,
    rejectionReason,
    setRejectionReason,
    formStates,
    formEnabledFlags,
    snoozeEntries,
    stageStatuses,
    calculateStageDates,
    handleFieldChange,
    handleActionClick,
    generateAlerts,
    sampleFeedback,
    adjustDateForSnoozes,
    stageDates
  } = useSampleFeedback(enquiryId, onClose, onSuccess, enquiryStatusId,sampleDeliveredDate);

  const [isHistoryDialogOpen, setIsHistoryDialogOpen] = useState(false);
  const [selectedHistoryStage, setSelectedHistoryStage] = useState<
    string | null
  >(null);

  const showRejectionReason =
    selectedResponse === "sample_rejected" ||
    selectedResponse === "sample_redo";

  const handleViewHistory = (stageId: string) => {
    setSelectedHistoryStage(stageId);
    setIsHistoryDialogOpen(true);
  };

const isDeliveryConfirmed = 
  (sampleFeedback && sampleFeedback[0] && !!sampleFeedback[0]?.delivery_confirmation_date);
// When updating the processStages array:
const updatedProcessStages = processStages.map((stage) => {
  // Count active snoozes for this stage
  const snoozeCount = snoozeEntries.filter(
    entry => entry.process_stage === stage.id
  ).length;
  
  if (stage.id === "delivery_confirmation") {
    // Adjust date for delivery confirmation (5 days per snooze)
    const adjustedDate = adjustDateForSnoozes(stageDates.delivery, snoozeCount, 5);
    
    return {
      ...stage,
      status: {
        ...stage.status,
        color: stageStatuses.delivery_confirmation === "completed" ? "green" : "gray",
        label: stageStatuses.delivery_confirmation === "completed" ? "Completed" : "Pending",
        sublabel: stageStatuses.delivery_confirmation !== "completed" && adjustedDate
          ? `by ${adjustedDate}` 
          : "",
      },
    };
  } else if (stage.id === "testing_start") {
    // Adjust date for testing start (5 days per snooze)
    const adjustedDate = adjustDateForSnoozes(stageDates.testing, snoozeCount, 5);
    
    return {
      ...stage,
      status: {
        ...stage.status,
        color: stageStatuses.testing_start === "completed" ? "green" : "gray",
        label: stageStatuses.testing_start === "completed" ? "Completed" : "Pending",
        // Only show date if delivery is confirmed
        sublabel: stageStatuses.testing_start !== "completed" && isDeliveryConfirmed && adjustedDate
          ? `by ${adjustedDate}` 
          : "",
      },
    };
  } else if (stage.id === "feedback") {
    // Adjust date for feedback (10 days per snooze)
    const adjustedDate = adjustDateForSnoozes(stageDates.feedback, snoozeCount, 10);
    
    return {
      ...stage,
      status: {
        ...stage.status,
        color: "gray",
        label: "Pending",
        // Only show date if delivery is confirmed
        sublabel: isDeliveryConfirmed && adjustedDate 
          ? `by ${adjustedDate}` 
          : "",
      },
    };
  }
  return stage;
});


  const isStageValid = (stageId: string) => {
    const stageData = formStates[stageId];
    const stage = updatedProcessStages.find((s) => s.id === stageId);
    
    // Special handling for feedback stage
    if (stageId === "feedback") {
      // If response type is Accept Sample, no additional validation needed
      if (stageData.responseType === "Accept Sample") {
        return true;
      }
      
      // For Reject Sample or Request New Sample, both reason and remarks are required
      if (stageData.responseType === "Reject Sample" || stageData.responseType === "Request New Sample") {
        const hasReason = stageData.reason && stageData.reason.trim() !== "";
        const hasRemarks = stageData.remarks && stageData.remarks.trim() !== "";
        return hasReason && hasRemarks;
      }
    }

    // Standard validation for all other fields and stages
    return stage.details.every((field: any) => {
      if (!field.required) return true;
      const value = stageData[field.labelValue];
      return value !== undefined && value !== null && value.trim() !== "";
    });
  };

  const renderDeliveryConfirmationForm = () => (
    <>
      {formEnabledFlags.delivery_confirmation &&
        generateAlerts()["delivery_confirmation"] && (
          <div
            style={{
              display: "flex",
              justifyContent: "space-between",
              alignItems: "center",
              background: "#fef3c7",
              color: "#b45309",
              borderRadius: 6,
              padding: "8px 12px",
              marginBottom: 8,
              fontSize: 13,
              width: "100%",
            }}
          >
            <div>{generateAlerts()["delivery_confirmation"]}</div>
            <button
              className="underline text-black text-xs ml-4"
              onClick={() => handleViewHistory("delivery_confirmation")}
            >
              View History
            </button>
          </div>
        )}
      <div className="space-y-4">
        <div className="grid grid-cols-12 gap-3">
          <div className="space-y-1 col-span-12 sm:col-span-6">
            <Label className="text-12x text-gray-700">Delivery Date</Label>
            <Input
              type="date"
              value={formStates.delivery_confirmation.deliveryDate}
              onChange={(e) =>
                handleFieldChange(
                  "delivery_confirmation",
                  "deliveryDate",
                  e.target.value
                )
              }
              className="h-8 text-xs"
              required
              max={new Date().toISOString().split('T')[0]} // Add this line to restrict future dates
            />
          </div>
          <div className="space-y-1 col-span-12 sm:col-span-6">
            <Label className="text-12x text-gray-700">
             Feedback ETA (Days)
            </Label>
            <Input
              min={0}
              type="number"
              value={formStates.delivery_confirmation.targetQualification}
              onChange={(e) =>
                handleFieldChange(
                  "delivery_confirmation",
                  "targetQualification",
                  e.target.value
                )
              }
              className="h-8 text-xs"
              required
            />
          </div>
        </div>
        {formStates.delivery_confirmation.deliveryDate && formStates.delivery_confirmation.targetQualification && formEnabledFlags.delivery_confirmation === true && (
          <div className="text-xs text-blue-700 mt-2">
            {(() => {
              const deliveryDate = new Date(formStates.delivery_confirmation.deliveryDate);
              const etaDays = parseInt(formStates.delivery_confirmation.targetQualification, 10);
              if (isNaN(etaDays)) return null;

              // Calculate Testing Start Date
              const testStartOffset = Math.min(Math.floor(etaDays / 2), 15);
              const testingStartDate = new Date(deliveryDate);
              testingStartDate.setDate(deliveryDate.getDate() + testStartOffset);

              // Calculate Feedback Date
              const feedbackOffset = Math.min(etaDays, 30);
              const feedbackDate = new Date(deliveryDate);
              feedbackDate.setDate(deliveryDate.getDate() + feedbackOffset);

              return (
                <>
                  Testing Start is expected on <b>{testingStartDate.toLocaleDateString()}</b> (in <b>{testStartOffset}</b> days) and Feedback on <b>{feedbackDate.toLocaleDateString()}</b> (in <b>{feedbackOffset}</b> days).
                </>
              );
            })()}
          </div>
        )}
      </div>
    </>
  );

  const renderTestingStartForm = () => (
    <>
      {/* For testing_start stage */}
      {formEnabledFlags.testing_start && generateAlerts()["testing_start"] && (
        <div
          style={{
            display: "flex",
            justifyContent: "space-between",
            alignItems: "center",
            background: "#fef3c7",
            color: "#b45309",
            borderRadius: 6,
            padding: "8px 12px",
            marginBottom: 8,
            fontSize: 13,
            width: "100%",
          }}
        >
          <div>{generateAlerts()["testing_start"]}</div>
          <button
            className="underline text-black text-xs ml-4"
            onClick={() => handleViewHistory("testing_start")}
          >
            View History
          </button>
        </div>
      )}
      <div className="space-y-4">
        <div className="grid grid-cols-12 gap-3">
        <div className="space-y-1 col-span-12 sm:col-span-6">
            <Label className="text-12x text-gray-700">Testing Start Date</Label>
            <Input
              type="date"
              value={formStates.testing_start.testingStarted}
              onChange={(e) =>
                handleFieldChange(
                  "testing_start",
                  "testingStarted",
                  e.target.value
                )
              }
              className="h-8 text-xs"
              required
              max={new Date().toISOString().split('T')[0]} // Add this line to restrict future dates
            />
          </div>
          <div className="space-y-1 col-span-12 sm:col-span-6">
            <Label className="text-12x text-gray-700">Remarks</Label>
            <Input
              value={formStates.testing_start.remarks}
              onChange={(e) =>
                handleFieldChange("testing_start", "remarks", e.target.value)
              }
              className="h-8 text-xs"
              placeholder="Enter remarks..."
              required
            />
          </div>
         
        </div>
      </div>
    </>
  );

  const renderFeedbackForm = () => {
    const showRejectionReason =
      formStates.feedback.responseType === "Reject Sample";
    const showRedoReason =
      formStates.feedback.responseType === "Request New Sample";
    const rejectionReasons = showRejectionReason
      ? SAMPLE_REJECT_REASONS
      : showRedoReason
      ? SAMPLE_REDO_REASONS
      : [];

    return (
      <>
        {/* For feedback stage */}
        {formEnabledFlags.feedback && generateAlerts()["feedback"] && (
          <div
            style={{
              display: "flex",
              justifyContent: "space-between",
              alignItems: "center",
              background: "#fef3c7",
              color: "#b45309",
              borderRadius: 6,
              padding: "8px 12px",
              marginBottom: 8,
              fontSize: 13,
              width: "100%",
            }}
          >
            <div>{generateAlerts()["feedback"]}</div>
            <button
              className="underline text-black text-xs ml-4"
              onClick={() => handleViewHistory("feedback")}
            >
              View History
            </button>
          </div>
        )}
        <div className="space-y-4">
          <div className="grid grid-cols-12 gap-3">
            <div className="space-y-1 col-span-12 sm:col-span-6">
              <Label className="text-12x text-gray-700">Response Type</Label>
              <Select
                value={formStates.feedback.responseType}
                onValueChange={(value) =>
                  handleFieldChange("feedback", "responseType", value)
                }
              >
                <SelectTrigger className="w-full h-8 text-xs">
                  <SelectValue placeholder="Select response" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="Accept Sample">
                    <div className="flex items-center gap-1.5">
                      <CheckCircle className="h-4 w-4 text-green-600" />
                      <span>Accept Sample</span>
                    </div>
                  </SelectItem>
                  <SelectItem value="Reject Sample">
                    <div className="flex items-center gap-1.5">
                      <XCircle className="h-4 w-4 text-red-600" />
                      <span>Reject Sample</span>
                    </div>
                  </SelectItem>
                  <SelectItem value="Request New Sample">
                    <div className="flex items-center gap-1.5">
                      <RefreshCw className="h-4 w-4 text-amber-600" />
                      <span>Request New Sample</span>
                    </div>
                  </SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-1 col-span-12 sm:col-span-6">
              <Label className="text-12x text-gray-700">
                Reason {(showRejectionReason || showRedoReason) && <span className="text-red-500">*</span>}
              </Label>
              {showRejectionReason || showRedoReason ? (
                <Select
                  value={formStates.feedback.reason}
                  onValueChange={(value) =>
                    handleFieldChange("feedback", "reason", value)
                  }
                >
                  <SelectTrigger className="w-full h-8 text-xs">
                    <SelectValue placeholder="Select a reason" />
                  </SelectTrigger>
                  <SelectContent>
                    {rejectionReasons.map((reason) => (
                      <SelectItem key={reason} value={reason}>
                        {reason}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              ) : (
                <Select disabled>
                  <SelectTrigger className="w-full h-8 text-xs">
                    <SelectValue placeholder="N/A" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="na">N/A</SelectItem>
                  </SelectContent>
                </Select>
              )}
            </div>
          </div>

          <div className="grid grid-cols-12 gap-3">
            <div className="space-y-1 col-span-12 sm:col-span-6">
              <Label className="text-12x text-gray-700">
                Remarks {(showRejectionReason || showRedoReason) && <span className="text-red-500">*</span>}
              </Label>
              <Input
                value={formStates.feedback.remarks}
                onChange={(e) =>
                  handleFieldChange("feedback", "remarks", e.target.value)
                }
                className="h-8 text-xs"
                placeholder="Enter feedback remarks..."
              />
            </div>

            <div className="space-y-1 col-span-12 sm:col-span-6">
              <Label className="text-12x text-gray-700">Attachment</Label>
              <CompactDocumentUpload
                onFilesChange={(files) => {
                  if (files && files.length > 0) {
                    handleFieldChange("feedback", "attachment", files[0]);
                  }
                }}
                files={
                  formStates.feedback.attachment
                    ? [formStates.feedback.attachment]
                    : []
                }
              />
            </div>
          </div>
        </div>
      </>
    );
  };

  // Clear rejection reason when response type changes
  useEffect(() => {
    if (!showRejectionReason && rejectionReason) {
      setRejectionReason("");
    }
  }, [selectedResponse, showRejectionReason, rejectionReason]);

  // Check if enquiryId exists
  useEffect(() => {
    if (!enquiryId) {
      console.error("Missing enquiry ID in SampleFeedbackForm");
      toast.error("Cannot submit feedback: Missing enquiry ID");
    }
  }, [enquiryId]);

  return (
    <div>
      <div
        style={{
          borderRadius: 12,
          background: "#fff",
          boxShadow: "0 2px 8px #eee",
          padding: 24,
          margin: 24,
          minWidth: 900,
        }}
      >
        <table
          style={{
            width: "100%",
            borderCollapse: "separate",
            borderSpacing: 0,
          }}
        >
          <div>
            <tr>
              <th
                style={{
                  textAlign: "left",
                  padding: 12,
                  fontWeight: 600,
                  color: "#6b7280",
                  maxWidth: 265,
                  minWidth: 265,
                }}
              >
                Process Stage
              </th>
              <th
                style={{
                  textAlign: "left",
                  padding: 12,
                  fontWeight: 600,
                  color: "#6b7280",
                  maxWidth: 700,
                  minWidth: 700,
                }}
              >
                Details
              </th>
              <th
                style={{
                  textAlign: "left",
                  padding: 12,
                  fontWeight: 600,
                  color: "#6b7280",
                }}
              >
                Actions
              </th>
            </tr>
          </div>
          <tbody>
            <div className="border border-gray-100 rounded-md bg-white/80 overflow-hidden w-full">
              {updatedProcessStages.map((stage, idx) => (
                <div
                  key={stage.id}
                  className={`p-2 ${idx > 0 ? "border-t border-gray-100" : ""}`}
                >
                  <tr>
                    <td
                      style={{
                        verticalAlign: "top",
                        padding: 16,
                        maxWidth: 400,
                        minWidth: 250,
                      }}
                    >
                      <div
                        style={{
                          display: "flex",
                          alignItems: "center",
                          gap: 8,
                        }}
                      >
                        <span
                          style={{
                            display: "inline-block",
                            width: 12,
                            height: 12,
                            borderRadius: "50%",
                            background:
                              statusColors[stage.status.color] || "#d1d5db",
                          }}
                        />
                        <span style={{ fontWeight: 600 }}>{stage.label}</span>
                      </div>
                      <div
                        style={{ color: "#6b7280", fontSize: 14, marginTop: 4 }}
                      >
                        {stage.status.label}
                      </div>
                      {stage.status.sublabel && (
                        <div style={{ color: "#9ca3af", fontSize: 13 }}>
                          {stage.status.sublabel}
                        </div>
                      )}
                    </td>
                    <td
                      style={{
                        verticalAlign: "top",
                        padding: 16,
                        minWidth: 700,
                      }}
                    >
                      {stage.id === "delivery_confirmation" &&
                        renderDeliveryConfirmationForm()}
                      {stage.id === "testing_start" && renderTestingStartForm()}
                      {stage.id === "feedback" && renderFeedbackForm()}
                    </td>
                    <td
                      style={{
                        verticalAlign: "top",
                        padding: 16,
                        minWidth: 200,
                      }}
                    >
                      <div
                        style={{
                          display: "flex",
                          flexDirection: "column",
                          gap: 12,
                        }}
                      >
                        {stage.id === "delivery_confirmation" && (
                          <>
                            <button
                              style={{
                                padding: "8px 8px",
                                borderRadius: 6,
                                border: "none",
                                background:
                                  formEnabledFlags.delivery_confirmation &&
                                  isStageValid("delivery_confirmation")
                                    ? "#22c55e"
                                    : "#f3f4f6",
                                color:
                                  formEnabledFlags.delivery_confirmation &&
                                  isStageValid("delivery_confirmation")
                                    ? "#fff"
                                    : "#374151",
                                fontWeight: 400,
                                cursor:
                                  formEnabledFlags.delivery_confirmation &&
                                  isStageValid("delivery_confirmation")
                                    ? "pointer"
                                    : "not-allowed",
                                opacity:
                                  formEnabledFlags.delivery_confirmation &&
                                  isStageValid("delivery_confirmation")
                                    ? 1
                                    : 0.5,
                              }}
                              onClick={() =>
                                handleActionClick("delivery_confirmation", {
                                  type: "submit_handler",
                                  label: "Confirm",
                                })
                              }
                              disabled={
                                !formEnabledFlags.delivery_confirmation ||
                                !isStageValid("delivery_confirmation")
                              }
                            >
                              Confirm
                            </button>
                            <button
                              style={{
                                padding: "8px 8px",
                                borderRadius: 6,
                                border: "none",
                                background:
                                  formEnabledFlags.delivery_confirmation
                                    ? "#f3f4f6"
                                    : "#f3f4f6",
                                color: formEnabledFlags.delivery_confirmation
                                  ? "#374151"
                                  : "#374151",
                                fontWeight: 400,
                                cursor: formEnabledFlags.delivery_confirmation
                                  ? "pointer"
                                  : "not-allowed",
                                opacity: formEnabledFlags.delivery_confirmation
                                  ? 1
                                  : 0.5,
                              }}
                              onClick={() =>
                                handleActionClick("delivery_confirmation", {
                                  type: "snooze",
                                  label: "Snooze (5 days)",
                                })
                              }
                              disabled={!formEnabledFlags.delivery_confirmation}
                            >
                              Snooze (5 days)
                            </button>
                          </>
                        )}
                        {stage.id === "testing_start" && (
                          <>
                            <button
                              style={{
                                padding: "8px 8px",
                                borderRadius: 6,
                                border: "none",
                                background:
                                  formEnabledFlags.testing_start &&
                                  isStageValid("testing_start")
                                    ? "#22c55e"
                                    : "#f3f4f6",
                                color:
                                  formEnabledFlags.testing_start &&
                                  isStageValid("testing_start")
                                    ? "#fff"
                                    : "#374151",
                                fontWeight: 400,
                                cursor:
                                  formEnabledFlags.testing_start &&
                                  isStageValid("testing_start")
                                    ? "pointer"
                                    : "not-allowed",
                                opacity:
                                  formEnabledFlags.testing_start &&
                                  isStageValid("testing_start")
                                    ? 1
                                    : 0.5,
                              }}
                              onClick={() =>
                                handleActionClick("testing_start", {
                                  type: "submit_handler",
                                  label: "Save",
                                })
                              }
                              disabled={
                                !formEnabledFlags.testing_start ||
                                !isStageValid("testing_start")
                              }
                            >
                              Save
                            </button>
                            <button
                              style={{
                                padding: "8px 8px",
                                borderRadius: 6,
                                border: "none",
                                background: formEnabledFlags.testing_start
                                  ? "#f3f4f6"
                                  : "#f3f4f6",
                                color: formEnabledFlags.testing_start
                                  ? "#374151"
                                  : "#374151",
                                fontWeight: 400,
                                cursor: formEnabledFlags.testing_start
                                  ? "pointer"
                                  : "not-allowed",
                                opacity: formEnabledFlags.testing_start
                                  ? 1
                                  : 0.5,
                              }}
                              onClick={() =>
                                handleActionClick("testing_start", {
                                  type: "snooze",
                                  label: "Snooze (5 days)",
                                })
                              }
                              disabled={!formEnabledFlags.testing_start}
                            >
                              Snooze (5 days)
                            </button>
                          </>
                        )}
                        {stage.id === "feedback" && (
                          <>
                            <button
                              style={{
                                padding: "8px 8px",
                                borderRadius: 6,
                                border: "none",
                                background:
                                  formEnabledFlags.feedback &&
                                  isStageValid("feedback")
                                    ? "#22c55e"
                                    : "#f3f4f6",
                                color:
                                  formEnabledFlags.feedback &&
                                  isStageValid("feedback")
                                    ? "#fff"
                                    : "#374151",
                                fontWeight: 400,
                                cursor:
                                  formEnabledFlags.feedback &&
                                  isStageValid("feedback")
                                    ? "pointer"
                                    : "not-allowed",
                                opacity:
                                  formEnabledFlags.feedback &&
                                  isStageValid("feedback")
                                    ? 1
                                    : 0.5,
                              }}
                              onClick={() =>
                                handleActionClick("feedback", {
                                  type: "handleSubmit",
                                  label: "Submit",
                                })
                              }
                              disabled={
                                !formEnabledFlags.feedback ||
                                !isStageValid("feedback")
                              }
                            >
                              Submit
                            </button>
                            <button
                              style={{
                                padding: "8px 8px",
                                borderRadius: 6,
                                border: "none",
                                background: formEnabledFlags.feedback
                                  ? "#f3f4f6"
                                  : "#f3f4f6",
                                color: formEnabledFlags.feedback
                                  ? "#374151"
                                  : "#374151",
                                fontWeight: 400,
                                cursor: formEnabledFlags.feedback
                                  ? "pointer"
                                  : "not-allowed",
                                opacity: formEnabledFlags.feedback ? 1 : 0.5,
                              }}
                              onClick={() =>
                                handleActionClick("feedback", {
                                  type: "snooze",
                                  label: "Snooze (10 days)",
                                })
                              }
                            >
                              Snooze (10 days)
                            </button>
                          </>
                        )}
                      </div>
                    </td>
                  </tr>
                </div>
              ))}
            </div>
          </tbody>
        </table>
      </div>
      {/* Snooze History Dialog */}
      <Dialog open={isHistoryDialogOpen} onOpenChange={setIsHistoryDialogOpen}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle>
              Snooze History - {selectedHistoryStage?.replace("_", " ")}
            </DialogTitle>
          </DialogHeader>
          <div className="py-4">
            <div className="max-h-[60vh] overflow-auto">
              <table className="w-full text-sm">
                <thead>
                  <tr className="border-b">
                    <th className="text-left py-2 px-4 font-medium">
                      Snooze Start Date
                    </th>
                    <th className="text-left py-2 px-4 font-medium">
                      Snooze End Date
                    </th>
                  </tr>
                </thead>
                <tbody>
                  {snoozeEntries
                    .filter(
                      (entry) => entry.process_stage === selectedHistoryStage
                    )
                    .map((entry, index) => {
                      const startDate = new Date(entry.snooze_started_at);
                      const formattedStartDate = startDate.toLocaleDateString(
                        "en-GB",
                        {
                          day: "2-digit",
                          month: "2-digit",
                          year: "numeric",
                        }
                      );

                      let formattedEndDate = "Active";
                      if (entry.snooze_ended_at) {
                        const endDate = new Date(entry.snooze_ended_at);
                        formattedEndDate = endDate.toLocaleDateString("en-GB", {
                          day: "2-digit",
                          month: "2-digit",
                          year: "numeric",
                        });
                      }

                      return (
                        <tr
                          key={index}
                          className={index % 2 === 0 ? "bg-gray-50" : ""}
                        >
                          <td className="py-2 px-4">{formattedStartDate}</td>
                          <td className="py-2 px-4">
                            {formattedEndDate === "Active" ? (
                              <span className="text-green-600 font-medium">
                                {formattedEndDate}
                              </span>
                            ) : (
                              formattedEndDate
                            )}
                          </td>
                        </tr>
                      );
                    })}
                  {snoozeEntries.filter(
                    (entry) => entry.process_stage === selectedHistoryStage
                  ).length === 0 && (
                    <tr>
                      <td
                        colSpan={2}
                        className="py-4 text-center text-gray-500"
                      >
                        No snooze history found
                      </td>
                    </tr>
                  )}
                </tbody>
              </table>
            </div>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default SampleFeedbackForm;
