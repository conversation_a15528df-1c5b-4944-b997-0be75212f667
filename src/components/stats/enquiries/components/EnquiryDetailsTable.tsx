import React, { useState } from 'react';
import { ChevronDown, ChevronUp, Paperclip } from 'lucide-react';
import EnquiryDocumentsModal from './EnquiryDocumentsModal';

interface EnquiryDetailsTableProps {
  enquiry: any;
}

const EnquiryDetailsTable: React.FC<EnquiryDetailsTableProps> = ({
  enquiry
}) => {
  const [isExpanded, setIsExpanded] = useState(false);
  const [isDocumentsModalOpen, setIsDocumentsModalOpen] = useState(false);

  console.log("EnquiryDetailsTable rendering with:", enquiry);

  // Check if there are documents attached
  const hasDocuments = enquiry?.id ? true : false; // We'll let the modal handle checking for documents

  // Function to format currency with symbol
  const formatCurrency = (amount: number | null, currency: string | null) => {
    if (amount === null || amount === undefined) return '-';
    if (!currency) return amount.toString();

    let symbol = '';
    switch (currency) {
      case 'USD':
        symbol = '$';
        break;
      case 'EUR':
        symbol = '€';
        break;
      case 'GBP':
        symbol = '£';
        break;
      case 'INR':
        symbol = '₹';
        break;
      case 'CNY':
        symbol = '¥';
        break;
      case 'JPY':
        symbol = '¥';
        break;
      case 'AED':
        symbol = 'د.إ';
        break;
      default:
        symbol = '';
    }

    return `${symbol}${amount}`;
  };

  // Function to get criticality color
  const getCriticalityColor = (criticality: string | null) => {
    if (!criticality) return 'text-gray-500';

    switch (criticality.toLowerCase()) {
      case 'high':
        return 'text-red-600';
      case 'medium':
        return 'text-yellow-600';
      case 'low':
        return 'text-green-600';
      default:
        return 'text-gray-500';
    }
  };

  return (
    <div className="bg-gray-50 rounded-lg border border-gray-200 overflow-hidden mb-2 ml-4">
      {/* Header */}
      {/* <div className="px-4 py-2 bg-gray-100/50 flex justify-between items-center border-b border-gray-200">
        <h4 className="text-sm font-medium text-gray-700">Enquiry Details</h4>
      </div> */}

      {/* Table Container with horizontal scroll */}
      <div className="overflow-x-auto">
        <table className="w-full text-sm border border-gray-200 table-fixed">
          <thead className="bg-gray-100">
            <tr>
              <th className="p-2 text-left font-medium border-r w-[20%]">Chemical Name</th>
              <th className="p-2 text-left font-medium border-r w-[15%]">Quantity</th>
              <th className="p-2 text-left font-medium border-r w-[15%]">Incoterms</th>
              <th className="p-2 text-left font-medium border-r w-[20%]">Destination</th>
              <th className="p-2 text-left font-medium border-r w-[20%]">Target Price</th>
              <th className="p-2 text-left font-medium border-r w-[15%]">Criticality</th>
              <th className="p-2 text-left font-medium w-[15%]">Documents from Customer</th>
            </tr>
          </thead>
          <tbody>
            <tr>
              <td className="p-2 border-r border-t bg-gray-50">{enquiry?.chemical_name || '-'}</td>
              <td className="p-2 border-r border-t bg-gray-50">
                {enquiry?.quantity !== null && enquiry?.quantity !== undefined
                  ? `${enquiry.quantity} ${enquiry.quantity_unit || ''}`
                  : '-'}
              </td>
              <td className="p-2 border-r border-t bg-gray-50">{enquiry?.incoterms || '-'}</td>
              <td className="p-2 border-r border-t bg-gray-50">{enquiry?.destination || '-'}</td>
              <td className="p-2 border-r border-t bg-gray-50">
                {enquiry?.target_price ? formatCurrency(enquiry.target_price, enquiry.target_price_currency) : '-'}
              </td>
              <td className="p-2 border-r border-t bg-gray-50">
                <span className={`capitalize ${getCriticalityColor(enquiry?.confidence)}`}>
                  {enquiry?.confidence || '-'}
                </span>
              </td>
              <td className="p-2 border-t bg-gray-50">
                <div className="flex items-center gap-3">
                  {hasDocuments ? (
                    <button
                      className="flex items-center gap-1.5 text-gray-600 hover:text-gray-900 px-2 py-1 text-sm bg-gray-100 hover:bg-gray-200 rounded-md"
                      onClick={() => setIsDocumentsModalOpen(true)}
                    >
                      <Paperclip className="h-3.5 w-3.5" />
                      <span>View</span>
                    </button>
                  ) : (
                    '-'
                  )}

                  {/* Expand/Collapse Button */}
                  <button
                    className="flex items-center justify-center w-8 h-8 p-2 bg-gray-100 hover:bg-gray-200 rounded-md text-gray-600 hover:text-gray-900 ml-6"
                    onClick={() => setIsExpanded(!isExpanded)}
                  >
                    {isExpanded ? (
                      <ChevronUp className="h-5 w-5" />
                    ) : (
                      <ChevronDown className="h-5 w-5" />
                    )}
                  </button>
                </div>
              </td>
            </tr>

            {/* Second Row - Additional fields (only shown when expanded) */}
            {isExpanded && (
              <tr>
                <td colSpan={7} className="p-0 border-t">
                  <div className="p-0">
                    <table className="w-full text-sm border border-gray-200 table-fixed">
                      <thead className="bg-gray-100">
                        <tr>
                          <th className="p-2 text-left font-medium border-r w-[20%]">Packaging Type</th>
                          <th className="p-2 text-left font-medium border-r w-[15%]">Qty per Packaging</th>
                          <th className="p-2 text-left font-medium border-r w-[15%]">CAS Number</th>
                          <th className="p-2 text-left font-medium border-r w-[20%]">Application</th>
                          <th className="p-2 text-left font-medium border-r w-[20%]">Customer Procurement Volume</th>
                          <th className="p-2 text-left font-medium border-r w-[15%]">Expected Volume</th>
                          <th className="p-2 text-left font-medium w-[15%]">Remarks</th>
                        </tr>
                      </thead>
                      <tbody>
                        <tr>
                          <td className="p-2 border-r border-t bg-gray-50">{enquiry?.packaging_type || '-'}</td>
                          <td className="p-2 border-r border-t bg-gray-50">{enquiry?.qty_per_packaging || '-'} {enquiry?.qty_per_packaging_unit || ''}</td>
                          <td className="p-2 border-r border-t bg-gray-50">{enquiry?.cas_number || '-'}</td>
                          <td className="p-2 border-r border-t bg-gray-50">{enquiry?.application || '-'}</td>
                          <td className="p-2 border-r border-t bg-gray-50">
                            {enquiry?.procurement_volume ? `${enquiry.procurement_volume} ${enquiry.procurement_unit || ''}` : '-'}
                          </td>
                          <td className="p-2 border-r border-t bg-gray-50">
                            {enquiry?.expected_procurement_volume ? `${enquiry.expected_procurement_volume} ${enquiry.expected_procurement_unit || ''}` : '-'}
                          </td>
                          <td className="p-2 border-t bg-gray-50">{enquiry?.remarks || '-'}</td>
                        </tr>
                      </tbody>
                    </table>
                  </div>
                </td>
              </tr>
            )}
          </tbody>
        </table>
      </div>

      {/* Documents Modal */}
      {hasDocuments && (
        <EnquiryDocumentsModal
          isOpen={isDocumentsModalOpen}
          setIsOpen={setIsDocumentsModalOpen}
          enquiryId={enquiry.id}
        />
      )}
    </div>
  );
};

export default EnquiryDetailsTable;
