import { FileText, Clock, Edit, X, Check, Beaker, Info } from "lucide-react";
import EnquiryDetailsTable from "./components/EnquiryDetailsTable";
import { <PERSON><PERSON> } from "@/components/ui/button";
import EnquiryDocumentsModal from "./components/EnquiryDocumentsModal";
import CompactDocumentUpload from "@/components/enquiries/components/CompactDocumentUpload";
import { Dialog, DialogContent } from "@/components/ui/dialog";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";

import { Input as AntInput } from "antd";
import {
  LabeledSelect,
  LabeledSelectItem,
} from "@/components/ui/custom-select";

import { useQuery } from "@tanstack/react-query";
import { supabase, STORAGE_BUCKETS } from "@/integrations/supabase/client";
import { useState, useCallback, useEffect } from "react";
import { v4 as uuidv4 } from "uuid";
import { toast } from "sonner";
import { useStatsSubscription } from "../hooks/useStatsSubscription";

import { useFeedbackData } from "./hooks/useFeedbackData";
import { EnquiryType, useEnquiryDocuments } from "./hooks/useEnquiriesData";
import { useSampleRequests } from "./hooks/useSampleRequests";
import SampleRequestForm from "./components/sample-request/SampleRequestForm";
import SampleRequestsList from "./components/SampleRequestsList";

// Import timeline components
import EnquiryCreatedItem from "./components/timeline/EnquiryCreatedItem";
import EnquiryAssignedItem from "./components/timeline/EnquiryAssignedItem";
import PricingQuotationGeneratedItem from "./components/timeline/PricingQuotationGeneratedItem";
import FollowUpScheduledItem from "./components/timeline/FollowUpScheduledItem";
import ClarificationNeededItem from "./components/timeline/ClarificationNeededItem";
import PORaisedItem from "./components/timeline/PORaisedItem";
import { useLocation, useNavigate } from "react-router-dom";

import "./styles/timeline.css";
import "@/components/enquiries/styles/enquiry-form.css";
import { createMeetingSummary } from "@/lib/utils";

interface EnquiryLifecycleDialogProps {
  selectedEnquiry: EnquiryType | null;
  isOpen: boolean;
  onClose: () => void;
  refetch?: () => void;
}

const CANCELLATION_REASONS = [
  "Customer bought from someone else",
  "Customer not responding",
  "Customer is not interested in the product anymore",
  "Others",
] as const;

const EnquiryLifecycleDialog = ({
  selectedEnquiry: selectedEnquiryProp,
  isOpen,
  onClose,
  refetch,
}: EnquiryLifecycleDialogProps) => {
  const [showValidationErrors, setShowValidationErrors] = useState(false);
  const [invalidFields, setInvalidFields] = useState<string[]>([]);
  const [selectedEnquiry, setSelectedEnquiry] = useState(selectedEnquiryProp);
  const enquiryId = selectedEnquiryProp?.id;
  // Initialize activeTab based on the current status of the enquiry
  const [activeTab, setActiveTab] = useState(() => {
    // If the enquiry is in one of the sample-related states, default to the "timeline" tab (Sample Details)
    const sampleRelatedStates = [
      "sample_requested",
      "sample_available",
      "sample_in_transit",
      "sample_delivered",
      "sample_redo"
    ];

    if (selectedEnquiryProp && sampleRelatedStates.includes(selectedEnquiryProp.current_status)) {
      return "timeline";
    }

    // Otherwise, default to the "details" tab
    return "details";
  });
  // ADD THESE NEW STATES:
  const [selectedCancellationReason, setSelectedCancellationReason] =
    useState("");
  const [customCancellationReason, setCustomCancellationReason] = useState("");

  const sampleTab =  sessionStorage.getItem('sampleTab');
  const [showRequestButton, setShowRequestButton] = useState(false);
  const [sampleRequestDropdownData, setSampleRequestDropdownData] = useState([]);


  useEffect(() => {
     if(sampleTab === "true") {
      setActiveTab("timeline")
     }
  }, [sampleTab]);
  
  // Fetch enquiry data
  const {
    data: enquiryData,
    isLoading: isLoadingEnquiry,
    refetch: refetchEnquiry,
  } = useQuery({
    queryKey: ["enquiry-details", enquiryId],
    enabled: !!enquiryId && isOpen,
    queryFn: async () => {
      const { data, error } = await supabase
        .from("enquiries")
        .select(
          `
          *,
          enquiry_documents(*)
        `
        )
        .eq("id", enquiryId)
        .single();

      if (error) throw error;
      setSelectedEnquiry(data as any);
      return data;
    },
  });

  // Get the current user's email
  const { data: session } = useQuery({
    queryKey: ["auth-session"],
    queryFn: async () => {
      const {
        data: { session },
      } = await supabase.auth.getSession();
      return session;
    },
  });

  // Fetch status history
  const {
    data: statusHistory,
    isLoading: isLoadingHistory,
    refetch: refetchStatusHistory,
  } = useQuery({
    queryKey: ["enquiry-status-history", enquiryId],
    enabled: !!enquiryId && isOpen,
    queryFn: async () => {
      const { data, error } = await supabase
        .from("enquiry_status_history")
        .select("*")
        .eq("enquiry_id", enquiryId)
        .order("created_at", { ascending: false });
      console.log("Status history data:", data);
      if (error) throw error;
      return data;
    },
  });

  // We don't need to use the sample request status anymore as each SampleTrackingItem fetches its own data

  // Get feedback data
  const { quotationFeedback } = useFeedbackData(enquiryId);

  // Get sample requests data
  const { sampleRequests, hasSampleRequests, refetch: refetchSampleRequests } = useSampleRequests(enquiryId);

  // Create a combined refetch function
  const refetchAll = useCallback(() => {
    refetchEnquiry();
    refetchStatusHistory();
    refetchSampleRequests();
    refetch?.();
  }, [ refetchStatusHistory, refetchSampleRequests, refetch]);

  // Setup real-time subscriptions
  useStatsSubscription(session?.user?.email, refetchAll);

  // Fetch documents for the selected enquiry
  const { data: documents } = useEnquiryDocuments(enquiryId);

  // Update selectedEnquiry when selectedEnquiryProp or documents change
  useEffect(() => {
    if (selectedEnquiryProp) {
      if (documents && documents.length > 0) {
        // If we have documents, add them to the selected enquiry
        setSelectedEnquiry({
          ...selectedEnquiryProp,
          enquiry_documents: documents
        });
      } else {
        // If no documents or still loading, just set the enquiry as is
        setSelectedEnquiry(selectedEnquiryProp);
      }

      // Update the active tab based on the current status of the enquiry
      const sampleRelatedStates = [
        "sample_requested",
        "sample_available",
        "sample_in_transit",
        "sample_delivered",
        "sample_redo"
      ];

      if (sampleRelatedStates.includes(selectedEnquiryProp.current_status) && hasSampleRequests) {
        setActiveTab("timeline");
      }
    }
  }, [selectedEnquiryProp, documents, hasSampleRequests]);

  // State for documents modal
  const [isDocumentsModalOpen, setIsDocumentsModalOpen] = useState(false);

  // Determine if the enquiry can be edited based on its status
  // This is used in the UI conditionally to show/hide edit buttons
  const [cancelReason, setCancelReason] = useState("");
  const [isEditing, setIsEditing] = useState(false);
  const [isCancelling, setIsCancelling] = useState(false);
  const [editFormData, setEditFormData] = useState({
    chemical_name: "",
    incoterms: "",
    quantity: null as number | null,
    quantity_unit: "",
    destination: "",
    cas_number: "",
    confidence: "",
    application: "", // Added separate field for application
    remarks: "",
    target_price: null as number | null,
    target_price_currency: "",
    procurement_volume: null as number | null,
    procurement_unit: "",
    expected_procurement_volume: null as number | null,
    expected_procurement_unit: "",
    packaging_type: "", // Add packaging type
    qty_per_packaging: null as number | null, // Add qty per packaging
    qty_per_packaging_unit: "", // Add qty per packaging unit
    attachedFiles: [] as File[],
  });

  // Add this state for sample request form
  const [showSampleRequestForm, setShowSampleRequestForm] = useState(sessionStorage.getItem('isOpenSampleRequest') === "true" ? true : false);


  // Function to handle enquiry cancellation
  const handleCancel = async () => {
    if (!selectedCancellationReason) {
      toast.error("Please select a reason for cancellation.");
      return;
    }
    if (
      selectedCancellationReason === "Others" &&
      !customCancellationReason.trim()
    ) {
      toast.error("Please provide a custom reason for cancellation.");
      return;
    }

    const finalReason =
      selectedCancellationReason === "Others"
        ? customCancellationReason.trim()
        : selectedCancellationReason;

    if (!selectedEnquiry?.id) return;

    try {
      // Update the enquiry status to "cancelled" in the Supabase enquiries table
      const { error } = await supabase
        .from("enquiries")
        .update({
          current_status: "cancelled",
          last_status_change: new Date().toISOString(),
        })
        .eq("id", selectedEnquiry.id);

      if (error) throw error;

      const {
        data: { session },
      } = await supabase.auth.getSession();
      if (!session) {
        toast.error("You must be logged in to cancel an enquiry");
        return;
      }

      // Add entry to status history
      const { error: historyError } = await supabase
        .from("enquiry_status_history")
        .insert({
          enquiry_id: selectedEnquiry.id,
          status: "cancelled",
          changed_by: session.user.email,
          sales_agent_email: session.user.email || "",
          notes: finalReason,
        });

      if (historyError) throw historyError;

      // Refetch data to update the UI
      refetchAll();
      setIsCancelling(false);
      setCancelReason("");
      setCustomCancellationReason("");
      setSelectedCancellationReason("");
      toast.success("Enquiry cancelled successfully");
      onClose(); // Close the dialog after cancellation
    } catch (error) {
      console.error("Error cancelling enquiry:", error);
      toast.error("Failed to cancel the enquiry. Please try again.");
    }
  };

  // Function to upload documents
  const uploadDocuments = async (files: File[], enquiryId: string) => {
    const {
      data: { session },
    } = await supabase.auth.getSession();
    if (!session) return;

    for (const file of files) {
      const fileExt = file.name.split(".").pop();
      const filePath = `${enquiryId}/${uuidv4()}.${fileExt}`;

      const { error: uploadError } = await supabase.storage
        .from(STORAGE_BUCKETS.ENQUIRY_DOCUMENTS)
        .upload(filePath, file);

      if (uploadError) {
        console.error("Error uploading file:", uploadError);
        toast.error(`Failed to upload ${file.name}`);
        continue;
      }

      const { error: metadataError } = await supabase
        .from("enquiry_documents")
        .insert({
          enquiry_id: enquiryId,
          file_name: file.name,
          file_path: filePath,
          content_type: file.type,
          size: file.size,
          uploaded_by: session.user.id,
        });

      if (metadataError) {
        console.error("Error saving file metadata:", metadataError);
        toast.error(`Failed to save metadata for ${file.name}`);
      }
    }
  };

  // Helper function to format timestamp
  const formatTimestamp = (timestamp: string | null | undefined) => {
    if (!timestamp) return "";
    const date = new Date(timestamp);
    return `${date.toLocaleDateString("en-GB", {
      day: "2-digit",
      month: "2-digit",
      year: "numeric",
    })}`;
  };

  // Helper function to format time
  const formatTime = (timestamp: string | null | undefined) => {
    if (!timestamp) return "";
    const date = new Date(timestamp);
    return date.toLocaleTimeString("en-GB", {
      hour: "2-digit",
      minute: "2-digit",
    });
  };

  // Process status history items
  const processedStatusHistory = statusHistory
    ? [...statusHistory].sort((a, b) => {
        // Sort by created_at in descending order (newest first)
        return (
          new Date(b.created_at).getTime() - new Date(a.created_at).getTime()
        );
      })
    : [];

    console.log("Processed Status History:", processedStatusHistory);


      // Then add this useEffect to update the flag when processedStatusHistory changes:
    useEffect(() => {
      if (!processedStatusHistory || processedStatusHistory.length === 0) {
        setShowRequestButton(false);
        return;
      }

      // Check if latest status is po_raised
      const latestStatus = processedStatusHistory[0]?.status;
      const isLatestPORaised = latestStatus === "po_raised";

      // Check if there's no pricing_quotation_generated entry
      const hasPricingQuotation = processedStatusHistory.some(
        item => item.status === "pricing_quotation_generated"
      );

      // Show button only if latest is po_raised AND no pricing quotation exists
      setShowRequestButton(isLatestPORaised && !hasPricingQuotation);
    }, [processedStatusHistory]);

  // We don't need to filter sample-related statuses anymore as each SampleTrackingItem fetches its own data

  // We'll map through the entire processedStatusHistory array instead of filtering for specific status types

  // Check if follow-up is scheduled
  const hasFollowUp = false; // This would be determined by your business logic

  const handleRequestNewQuotation = async(quoteGeneration : any = {}) => {
    // TODO: Replace with your actual logic/modal for requesting a new quotation
    const enquiryId = quoteGeneration?.enquiry_id || selectedEnquiry?.id
    if (!enquiryId) {
    toast.error("No enquiry ID found for this quotation.");
    return;
  }

  try {
    // Get current timestamp in UTC (ISO format)
    const currentTimestamp = new Date().toISOString();

    // 1. Update enquiries table with new status and timestamp
    const { error: enquiryError } = await supabase
      .from("enquiries")
      .update({
        current_status: "quote_revision_needed",
        last_status_change: currentTimestamp
      })
      .eq("id",enquiryId);

    if (enquiryError) throw enquiryError;

     // 2. Fetch latest procurement_poc from status history
     const { data: lastHistory, error: lastHistoryError } = await supabase
     .from("enquiry_status_history")
     .select("sales_agent_email, procurement_poc")
     .eq("enquiry_id", enquiryId)
     .order("created_at", { ascending: false })
     .limit(1)
     .single();

     if (lastHistoryError) throw lastHistoryError;

    // 2. Insert into enquiry_status_history
    const { data: sessionData } = await supabase.auth.getSession();
    const userId = sessionData?.session?.user?.id || "";

    const { error: historyError } = await supabase
      .from("enquiry_status_history")
      .insert({
        enquiry_id: enquiryId,
        status: "quote_revision_needed",
        changed_by: userId,
        sales_agent_email: lastHistory?.sales_agent_email || null,
        procurement_poc: lastHistory?.procurement_poc || null,
        notes: "Requested new quotation due to expiry.",
      });

    if (historyError) throw historyError;

    toast.success("Quote Revision has been requested to Sourcing Team.");

    // Call the onRefresh callback if provided to update the UI without page refresh
    if (refetchAll) {
      refetchAll();
    }
  } catch (err) {
    toast.error("Failed to update enquiry status.");
    console.error(err);
  }
  };


const fetchCustomerPastAddresses = async (customerName: string) => {
  try {
    const { data, error } = await supabase
      .from("sample_requests")
      .select(
        `
        delivery_address,
        contact_email,
        contact_phone,
        created_at,
        sample_poc,
        enquiries!inner(
          customer_full_name
        )
      `
      )
      .eq("enquiries.customer_full_name", customerName)
      .order("created_at", { ascending: false });

    if (error) throw error;

    setSampleRequestDropdownData(data);
  } catch (error) {
    console.error("Error fetching customer addresses:", error);
    throw error;
  }
};

  useEffect(()=>{
    fetchCustomerPastAddresses(selectedEnquiry?.customer_full_name)
  },[selectedEnquiry])

  if (isLoadingHistory) {
    return (
      <Dialog open={isOpen} onOpenChange={(open) => !open && onClose()}>
        <DialogContent className="sm:max-w-[80vw] p-0 h-[85vh] overflow-hidden">
          <div className="p-6">
            <div className="flex justify-center items-center h-40">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900"></div>
            </div>
          </div>
        </DialogContent>
      </Dialog>
    );
  }

  return (
    <>
      <Dialog
        open={isOpen}
        onOpenChange={(open) => {
          if (!open) {
            // Reset all form states when dialog is closed
            setIsEditing(false);
            setIsCancelling(false);
            setShowSampleRequestForm(false);
            setCancelReason("");
            onClose();
          }
        }}
      >
        <DialogContent className="sm:max-w-[80vw] p-0 h-[85vh] overflow-hidden flex flex-col enquiry-form max-h-[85vh]">
          {/* Fixed header section */}
          <div className="flex-none">
            {/* Title bar */}
            <div className="px-6 py-1 pt-2 flex items-center justify-between">
              <div className="flex items-center">
                <span className="text-3xl font-bold">
                  {selectedEnquiry?.enquiry_id || ""}
                </span>
              </div>

              {!isEditing && !isCancelling ? (
                <div className="flex items-center gap-2">
                  <Button
                    variant="outline"
                    size="sm"
                    className="bg-purple-50 text-purple-700 hover:bg-purple-100 border-purple-200 mr-2"
                    onClick={() => {
                      setShowSampleRequestForm(!showSampleRequestForm);
                      setActiveTab("details"); // Switch to details tab when showing sample request form
                    }}
                  >
                    <Beaker className="h-4 w-4 mr-1" />
                    Raise a Sample Request
                  </Button>
                  {/* Cancel Button */}
                  {selectedEnquiry?.current_status !== "cancelled" &&
                    selectedEnquiry?.current_status !== "po_raised" &&
                    selectedEnquiry?.current_status !== "regret" &&
                    selectedEnquiry?.current_status !== "quote_rejected" &&
                    selectedEnquiry?.current_status !== "sample_rejected" && (
                      <Button
                        variant="outline"
                        size="sm"
                        className="bg-red-50 text-red-700 hover:bg-red-100 border-red-200 mr-2"
                        onClick={() => {
                          setIsCancelling(true);
                          setActiveTab("details"); // Switch to details tab when cancelling
                        }}
                      >
                        <X className="h-4 w-4 mr-1" />
                        Cancel Enquiry from Customer
                      </Button>
                    )}

                  <Button
                    variant="outline"
                    size="sm"
                    className="bg-gray-50 text-gray-700 hover:bg-gray-100 border-gray-200 mr-6"
                    onClick={() => {
                      setIsEditing(true);
                      setActiveTab("details"); // Switch to details tab when editing
                      setEditFormData({
                        chemical_name: selectedEnquiry?.chemical_name || "",
                        incoterms: selectedEnquiry?.incoterms || "",
                        quantity: selectedEnquiry?.quantity || null,
                        quantity_unit: selectedEnquiry?.quantity_unit || "",
                        destination: selectedEnquiry?.destination || "",
                        cas_number: selectedEnquiry?.cas_number || "",
                        confidence: selectedEnquiry?.confidence || "low",
                        application: selectedEnquiry?.application || "", // Initialize application field
                        remarks: selectedEnquiry?.remarks || "",
                        target_price: selectedEnquiry?.target_price || null,
                        target_price_currency:
                          selectedEnquiry?.target_price_currency || "",
                        procurement_volume:
                          selectedEnquiry?.procurement_volume || null,
                        procurement_unit:
                          selectedEnquiry?.procurement_unit || "",
                        expected_procurement_volume:
                          selectedEnquiry?.expected_procurement_volume || null,
                        expected_procurement_unit:
                          selectedEnquiry?.expected_procurement_unit || "",
                        packaging_type: selectedEnquiry?.packaging_type || "", // Initialize packaging type
                        qty_per_packaging: selectedEnquiry?.qty_per_packaging || null, // Initialize qty per packaging
                        qty_per_packaging_unit: selectedEnquiry?.qty_per_packaging_unit || "", // Initialize the new field
                        attachedFiles: [],
                      });
                    }}
                  >
                    <Edit className="h-4 w-4 mr-1" />
                    Edit
                  </Button>
                </div>
              ) : null}
            </div>

            {/* Metadata bar */}
            {isCancelling || isEditing || showSampleRequestForm ? (
              <>
                <div className="px-4 py-1 flex flex-wrap gap-x-4 gap-y-2 text-base justify-between bg-gray-50 rounded-md my-1 mx-2">
                  <div className="flex flex-wrap gap-x-4 gap-y-2 w-full">
                    {isCancelling ? (
                      <div className="w-full">
                        {/* Cancel Form */}
                        <div className="border border-gray-200 rounded-lg overflow-hidden mb-4">
                          {/* Form Header */}
                          <div className="p-3 bg-gray-50 border-b border-gray-200">
                            <div className="text-lg font-medium text-gray-700">
                              Cancel Enquiry
                            </div>
                          </div>

                          {/* Form Content */}
                          <div className="p-3 space-y-4">
                            {/* Dropdown for cancellation reasons */}
                            <div>
                              <label className="block text-sm font-medium text-gray-700 mb-2">
                                Reason for Cancellation{" "}
                                <span className="text-red-500">*</span>
                              </label>
                              <LabeledSelect
                                value={selectedCancellationReason}
                                onValueChange={setSelectedCancellationReason}
                                placeholder="Select cancellation reason"
                                triggerClassName="w-full h-10 bg-white"
                              >
                                {CANCELLATION_REASONS.map((reason) => (
                                  <LabeledSelectItem
                                    key={reason}
                                    value={reason}
                                    label={reason}
                                  >
                                    {reason}
                                  </LabeledSelectItem>
                                ))}
                              </LabeledSelect>
                            </div>

                            {/* Custom reason input - only show if "Others" is selected */}
                            {selectedCancellationReason === "Others" && (
                              <div>
                                <label className="block text-sm font-medium text-gray-700 mb-2">
                                  Please specify the reason{" "}
                                  <span className="text-red-500">*</span>
                                </label>
                                <AntInput.TextArea
                                  value={customCancellationReason}
                                  onChange={(e) =>
                                    setCustomCancellationReason(e.target.value)
                                  }
                                  placeholder="Enter custom reason for cancellation..."
                                  className="w-full p-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-red-400 h-24"
                                />
                              </div>
                            )}
                          </div>

                          {/* Action Buttons */}
                          <div className="flex justify-end gap-2 p-3 bg-gray-50 border-t border-gray-200">
                            <Button
                              variant="outline"
                              size="sm"
                              className="border-gray-300 hover:bg-gray-50"
                              onClick={() => {
                                setIsCancelling(false);
                                setCancelReason("");
                                refetchAll();
                              }}
                            >
                              <X className="h-4 w-4 mr-1 text-gray-500 font-bold stroke-[2.5px]" />
                              <span className="text-gray-500 font-medium">
                                Cancel
                              </span>
                            </Button>

                            <Button
                              variant="outline"
                              size="sm"
                              className="border-red-500 hover:bg-red-50"
                              onClick={handleCancel}
                            >
                              <Check className="h-4 w-4 mr-1 text-red-500 font-bold stroke-[2.5px]" />
                              <span className="text-red-500 font-medium">
                                Confirm Cancel
                              </span>
                            </Button>
                          </div>
                        </div>
                      </div>
                    ) : showSampleRequestForm ? (
                      <div className="w-full">
                        {/* Sample Request Form */}
                        <div className="border border-purple-200 rounded-lg mb-4">
                          <div className="p-3">
                            <SampleRequestForm
                              enquiryId={enquiryId || ""}
                              sampleRequestDropdownData={sampleRequestDropdownData}
                              onClose={() => setShowSampleRequestForm(false)}
                              onSuccess={() => {
                                setShowSampleRequestForm(false);
                                toast.success("Sample request submitted successfully");
                                refetchAll();
                                // Switch to the Sample Details tab if it exists
                                if (hasSampleRequests) {
                                  setActiveTab("timeline");
                                }
                              }}
                            />
                          </div>
                        </div>
                      </div>
                    ) : isEditing ? (
                      <div className="w-full">
                        {/* Edit Form in Table Layout */}
                        <div className="border border-gray-200 rounded-lg overflow-hidden mb-4 overflow-x-auto">
                          <div className="min-w-[1000px]">
                            {/* Table Header */}
                            <div className="flex gap-4 p-3 bg-gray-50 border-b border-gray-200 rounded-t-lg">
                              <div className="text-sm font-medium text-gray-700" style={{ flex: "15%" }}>
                                Chemical Name{" "}
                                <span className="text-red-500">*</span>
                              </div>
                              <div className="text-sm font-medium text-gray-700" style={{ flex: "15%" }}>
                                Quantity <span className="text-red-500">*</span>
                              </div>
                              <div className="text-sm font-medium text-gray-700" style={{ flex: "10%" }}>
                                Incoterms{" "}
                                <span className="text-red-500">*</span>
                              </div>
                              <div className="text-sm font-medium text-gray-700" style={{ flex: "15%" }}>
                                Destination{" "}
                                <span className="text-red-500">*</span>
                              </div>
                              <div className="text-sm font-medium text-gray-700" style={{ flex: "20%" }}>
                                Target Price
                              </div>
                              <div className="text-sm font-medium text-gray-700" style={{ flex: "10%" }}>
                                Criticality{" "}
                                <span className="text-red-500">*</span>
                              </div>
                              <div className="text-sm font-medium text-gray-700" style={{ flex: "15%" }}>
                                Documents from Customer
                              </div>
                            </div>

                            {/* Form Fields */}
                            {/* First row - New order: Chemical Name, Quantity, Incoterms, Destination, Target Price, Criticality, Documents */}
                            <div className="flex gap-4 p-3 border-b border-gray-200">
                              {/* 1. Chemical Name */}
                              <div style={{ flex: "15%" }}>
                                <AntInput
                                  placeholder="Chemical name"
                                  value={editFormData.chemical_name}
                                  onChange={(e) =>
                                    setEditFormData({
                                      ...editFormData,
                                      chemical_name: e.target.value,
                                    })
                                  }
                                  className="w-full h-8 border border-input rounded-md"
                                  style={{
                                    borderColor: showValidationErrors && !editFormData.chemical_name ? 'red' : "#e5e7eb",
                                  }}
                                />
                              </div>

                              {/* Quantity */}
                              <div className="flex" style={{ flex: "15%" }}>
                                <input
                                  type="number"
                                  min="0"
                                  placeholder="Quantity"
                                  value={
                                    editFormData.quantity !== null
                                      ? editFormData.quantity
                                      : ""
                                  }
                                  onChange={(e) => {
                                    const value = e.target.value;
                                    setEditFormData({
                                      ...editFormData,
                                      quantity:
                                        value === "" ? null : parseFloat(value),
                                    });
                                  }}
                                  style={{
                                    width: "50%",
                                    borderColor: showValidationErrors && !editFormData.quantity ? 'red' : "#e5e7eb",
                                    textOverflow: "ellipsis",
                                    whiteSpace: "nowrap",
                                    overflow: "hidden",
                                  }}
                                  className="h-8 border border-input rounded-l-md text-left placeholder:text-center placeholder:text-xs placeholder:text-gray-400 [appearance:textfield] [&::-webkit-outer-spin-button]:appearance-none [&::-webkit-inner-spin-button]:appearance-none"
                                />
                                <div className="w-[50%]">
                                  <LabeledSelect
                                    value={editFormData.quantity_unit || ""}
                                    onValueChange={(value) =>
                                      setEditFormData({
                                        ...editFormData,
                                        quantity_unit: value,
                                      })
                                    }
                                    placeholder="Unit"
                                    triggerClassName="h-8 bg-white rounded-l-none border-l-0"
                                  >
                                    <LabeledSelectItem value="Metric Ton (mt)" label="Mt">
                                      Metric Ton (mt)
                                    </LabeledSelectItem>
                                    <LabeledSelectItem value="Pound (lb)" label="lb">
                                      Pound (lb)
                                    </LabeledSelectItem>
                                    <LabeledSelectItem value="Gallon (gal)" label="gal">
                                      Gallon (gal)
                                    </LabeledSelectItem>
                                    <LabeledSelectItem value="Litre (L)" label="L">
                                      Litre (L)
                                    </LabeledSelectItem>
                                    <LabeledSelectItem value="Kilolitre (Kl)" label="Kl">
                                      Kilolitre (Kl)
                                    </LabeledSelectItem>
                                    <LabeledSelectItem value="Kilogram (Kg)" label="Kg">
                                      Kilogram (Kg)
                                    </LabeledSelectItem>
                                  </LabeledSelect>
                                </div>
                              </div>

                              {/* Incoterms */}
                              <div style={{ flex: "10%" }}>
                                <LabeledSelect
                                  value={editFormData.incoterms || ""}
                                  onValueChange={(value) =>
                                    setEditFormData({
                                      ...editFormData,
                                      incoterms: value,
                                    })
                                  }
                                  placeholder="Select incoterms"
                                  triggerClassName="w-full h-8 bg-white"
                                >
                                  <LabeledSelectItem value="EXW (Ex Works)" label="EXW">
                                    EXW (Ex Works)
                                  </LabeledSelectItem>
                                  <LabeledSelectItem value="FOB (Free on board)" label="FOB">
                                    FOB (Free on board)
                                  </LabeledSelectItem>
                                  <LabeledSelectItem value="DDP (Delivery Duty Paid)" label="DDP">
                                    DDP (Delivery Duty Paid)
                                  </LabeledSelectItem>
                                  <LabeledSelectItem value="CIF (Cost, Insurance, & Freight)" label="CIF">
                                    CIF (Cost, Insurance, & Freight)
                                  </LabeledSelectItem>
                                  <LabeledSelectItem value="CFR (Cost & Freight)" label="CFR">
                                    CFR (Cost & Freight)
                                  </LabeledSelectItem>
                                  <LabeledSelectItem value="FOR (Free on road)" label="FOR">
                                    FOR (Free on road)
                                  </LabeledSelectItem>
                                </LabeledSelect>
                              </div>

                              {/* Destination */}
                              <div style={{ flex: "15%" }}>
                                <AntInput
                                  placeholder="Destinations"
                                  value={editFormData.destination}
                                  onChange={(e) =>
                                    setEditFormData({
                                      ...editFormData,
                                      destination: e.target.value,
                                    })
                                  }
                                  className="w-full h-8 border border-input rounded-md"
                                  style={{
                                    borderColor: showValidationErrors && !editFormData.destination ? 'red' : "#e5e7eb",
                                  }}
                                />
                              </div>

                              {/* Target Price */}
                              <div className="flex" style={{ flex: "20%" }}>
                                <input
                                  type="number"
                                  min="0"
                                  placeholder="Target price"
                                  value={
                                    editFormData.target_price !== null
                                      ? editFormData.target_price
                                      : ""
                                  }
                                  onChange={(e) => {
                                    const value = e.target.value;
                                    setEditFormData({
                                      ...editFormData,
                                      target_price: value === "" ? null : parseFloat(value),
                                    });
                                  }}
                                  style={{
                                    width: "70%",
                                    borderColor: "#e5e7eb",
                                    textOverflow: "ellipsis",
                                    whiteSpace: "nowrap",
                                    overflow: "hidden",
                                  }}
                                  className="h-8 border border-input rounded-l-md text-left placeholder:text-center placeholder:text-xs placeholder:text-gray-400 [appearance:textfield] [&::-webkit-outer-spin-button]:appearance-none [&::-webkit-inner-spin-button]:appearance-none"
                                />
                                <div className="w-[30%]">
                                  <LabeledSelect
                                    value={editFormData.target_price_currency || ""}
                                    onValueChange={(value) =>
                                      setEditFormData({
                                        ...editFormData,
                                        target_price_currency: value,
                                      })
                                    }
                                    placeholder="Currency"
                                    triggerClassName="h-8 bg-white rounded-l-none border-l-0"
                                  >
                                    <LabeledSelectItem value="USD" label="$">
                                      USD $
                                    </LabeledSelectItem>
                                    <LabeledSelectItem value="INR" label="₹">
                                      INR ₹
                                    </LabeledSelectItem>
                                    <LabeledSelectItem value="EURO" label="€">
                                      EUR €
                                    </LabeledSelectItem>
                                    <LabeledSelectItem value="YUAN" label="¥">
                                      CNY ¥
                                    </LabeledSelectItem>
                                    <LabeledSelectItem value="YEN" label="¥">
                                      JPY ¥
                                    </LabeledSelectItem>
                                    <LabeledSelectItem value="AED" label="د.إ">
                                      AED د.إ
                                    </LabeledSelectItem>
                                  </LabeledSelect>
                                </div>
                                {editFormData.quantity_unit && (
                                  <div className="flex items-center space-x-1">
                                  {/* Slash */}
                                  <div className="text-gray-800 select-none text-sm mx-1">/</div>

                                  {/* Unit Box */}
                                  <div
                                  className="text-sm text-gray-800 bg-gray-100 px-2 rounded-md flex items-center justify-center"
                                  style={{
                                    width: '2.2rem',
                                    height: '2rem'
                                  }}
                                  >
                                  {editFormData.quantity_unit === "Kilogram (Kg)" ? "Kg" :
                                  editFormData.quantity_unit === "Metric Ton (mt)" ? "Mt" :
                                  editFormData.quantity_unit === "Pound (lb)" ? "lb" :
                                  editFormData.quantity_unit === "Gallon (gal)" ? "gal" :
                                  editFormData.quantity_unit === "Litre (L)" ? "L" :
                                  editFormData.quantity_unit === "Kilolitre (Kl)" ? "Kl" :
                                  editFormData.quantity_unit}
                                  </div>
                                  </div>
                                  )}
                              </div>

                              {/* Criticality */}
                              <div style={{ flex: "10%" }}>
                                <LabeledSelect
                                  value={editFormData.confidence || ""}
                                  onValueChange={(value) =>
                                    setEditFormData({
                                      ...editFormData,
                                      confidence: value,
                                    })
                                  }
                                  placeholder="Select criticality"
                                  triggerClassName="w-full h-8 bg-white"
                                >
                                  <LabeledSelectItem value="low" label="Low">
                                    Low
                                  </LabeledSelectItem>
                                  <LabeledSelectItem
                                    value="medium"
                                    label="Medium"
                                  >
                                    Medium
                                  </LabeledSelectItem>
                                  <LabeledSelectItem value="high" label="High">
                                    High
                                  </LabeledSelectItem>
                                </LabeledSelect>
                              </div>

                              {/* Documents */}
                              <div style={{ flex: "15%" }}>
                                <CompactDocumentUpload
                                  files={editFormData.attachedFiles}
                                  onFilesChange={(files) =>
                                    setEditFormData({
                                      ...editFormData,
                                      attachedFiles: files,
                                    })
                                  }
                                  className="w-full"
                                />
                              </div>
                            </div>

                            {/* Row 2: Headers */}
                            <div className="grid grid-cols-7 gap-4 p-3 bg-gray-50 border-b border-gray-200">
                            <div className="text-sm font-medium text-gray-700">
                                Packaging Type
                              </div>
                              <div className="text-sm font-medium text-gray-700">
                                Qty per Packaging
                              </div>
                              <div className="text-sm font-medium text-gray-700">
                                CAS Number
                              </div>
                              <div className="text-sm font-medium text-gray-700">
                                Application
                              </div>
                              <div className="text-sm font-medium text-gray-700">
                                Customer's Procurement (Quarterly)
                              </div>
                              <div className="text-sm font-medium text-gray-700">
                                Expected Procurement (Quarterly)
                              </div>
                              <div className="text-sm font-medium text-gray-700">
                                Remarks
                              </div>
                            </div>

                            {/* Second row */}
                            <div className="grid grid-cols-7 gap-4 p-3 border-b border-gray-200">

                              {/* Packaging Type */}
                              <div>
                                <LabeledSelect
                                  value={editFormData.packaging_type || ""}
                                  onValueChange={(value) =>
                                    setEditFormData({
                                      ...editFormData,
                                      packaging_type: value,
                                    })
                                  }
                                  placeholder="Packaging Type"
                                  triggerClassName="w-full h-8 bg-white"
                                >
                                  <LabeledSelectItem value="ISO TANK" label="ISO TANK">ISO TANK</LabeledSelectItem>
                                  <LabeledSelectItem value="IBC" label="IBC">IBC</LabeledSelectItem>
                                  <LabeledSelectItem value="HDPE DRUM OPEN TOP" label="HDPE DRUM OPEN TOP">HDPE DRUM OPEN TOP</LabeledSelectItem>
                                  <LabeledSelectItem value="HDPE DRUM NARROW MOUTH" label="HDPE DRUM NARROW MOUTH">HDPE DRUM NARROW MOUTH</LabeledSelectItem>
                                  <LabeledSelectItem value="JERRY CAN" label="JERRY CAN">JERRY CAN</LabeledSelectItem>
                                  <LabeledSelectItem value="FIBRE BOARD DRUM" label="FIBRE BOARD DRUM">FIBRE BOARD DRUM</LabeledSelectItem>
                                  <LabeledSelectItem value="BAG" label="BAG">BAG</LabeledSelectItem>
                                  <LabeledSelectItem value="JUMBO BAG" label="JUMBO BAG">JUMBO BAG</LabeledSelectItem>
                                  <LabeledSelectItem value="CARTON BOX" label="CARTON BOX">CARTON BOX</LabeledSelectItem>
                                  <LabeledSelectItem value="CARDBOARD DRUM" label="CARDBOARD DRUM">CARDBOARD DRUM</LabeledSelectItem>
                                  <LabeledSelectItem value="HDPE BAG" label="HDPE BAG">HDPE BAG</LabeledSelectItem>
                                  <LabeledSelectItem value="MS DRUM" label="MS DRUM">MS DRUM</LabeledSelectItem>
                                  <LabeledSelectItem value="FIBRE DRUM OPEN TOP" label="FIBRE DRUM OPEN TOP">FIBRE DRUM OPEN TOP</LabeledSelectItem>
                                  <LabeledSelectItem value="TANKER TRUCK" label="TANKER TRUCK">TANKER TRUCK</LabeledSelectItem>
                                  <LabeledSelectItem value="CARBOYS" label="CARBOYS">CARBOYS</LabeledSelectItem>
                                  <LabeledSelectItem value="HDPE DRUM" label="HDPE DRUM">HDPE DRUM</LabeledSelectItem>
                                  <LabeledSelectItem value="PP BAG" label="PP BAG">PP BAG</LabeledSelectItem>
                                  <LabeledSelectItem value="HDPE BOTTLE" label="HDPE BOTTLE">HDPE BOTTLE</LabeledSelectItem>
                                  <LabeledSelectItem value="BOTTLE" label="BOTTLE">BOTTLE</LabeledSelectItem>
                                  <LabeledSelectItem value="PACKET" label="PACKET">PACKET</LabeledSelectItem>
                                  <LabeledSelectItem value="FLEXI BAG" label="FLEXI BAG">FLEXI BAG</LabeledSelectItem>
                                  <LabeledSelectItem value="STEEL DRUM" label="STEEL DRUM">STEEL DRUM</LabeledSelectItem>
                                  <LabeledSelectItem value="PAPER BAG" label="PAPER BAG">PAPER BAG</LabeledSelectItem>
                                </LabeledSelect>
                              </div>

                              {/* Qty per Packaging */}
                              <div>
                                <div className="flex">
                                  <input
                                    type="number"
                                    min="0"
                                    placeholder="Qty per Packaging"
                                    value={editFormData.qty_per_packaging !== null ? editFormData.qty_per_packaging : ""}
                                    onChange={(e) => {
                                      const value = e.target.value;
                                      setEditFormData({
                                        ...editFormData,
                                        qty_per_packaging: value === "" ? null : parseFloat(value),
                                      });
                                    }}
                                    style={{
                                      width: "50%",
                                      borderColor: "#e5e7eb",
                                      textOverflow: "ellipsis",
                                      whiteSpace: "nowrap",
                                      overflow: "hidden",

                                    }}
                                    className="h-8 border border-input rounded-l-md text-left placeholder:text-center placeholder:text-xs placeholder:text-gray-400 [appearance:textfield] [&::-webkit-outer-spin-button]:appearance-none [&::-webkit-inner-spin-button]:appearance-none "
                                    />
                                  <div className="w-[50%]">
                                    <LabeledSelect
                                      value={editFormData.qty_per_packaging_unit || ""}
                                      onValueChange={(value) =>
                                        setEditFormData({
                                          ...editFormData,
                                          qty_per_packaging_unit: value,
                                        })
                                      }
                                      placeholder="Unit"
                                      triggerClassName="h-8 bg-white rounded-l-none border-l-0"
                                    >
                                      <LabeledSelectItem value="Metric Ton (mt)" label="Mt">
                                        Metric Ton (mt)
                                      </LabeledSelectItem>
                                      <LabeledSelectItem value="Pound (lb)" label="Lb">
                                        Pound (lb)
                                      </LabeledSelectItem>
                                      <LabeledSelectItem value="Gallon (gal)" label="Gal">
                                        Gallon (gal)
                                      </LabeledSelectItem>
                                      <LabeledSelectItem value="Litre (L)" label="L">
                                        Litre (L)
                                      </LabeledSelectItem>
                                      <LabeledSelectItem value="Kilolitre (Kl)" label="Kl">
                                        Kilolitre (Kl)
                                      </LabeledSelectItem>
                                      <LabeledSelectItem value="Kilogram (Kg)" label="Kg">
                                        Kilogram (Kg)
                                      </LabeledSelectItem>
                                    </LabeledSelect>
                                  </div>
                                </div>
                              </div>

                                                            {/* CAS Number */}
                                                            <div>
                                <AntInput
                                  placeholder="CAS Number"
                                  value={editFormData.cas_number}
                                  onChange={(e) =>
                                    setEditFormData({
                                      ...editFormData,
                                      cas_number: e.target.value,
                                    })
                                  }
                                  className="w-full h-8 border border-input rounded-md"
                                  style={{ borderColor: "#e5e7eb" }}
                                />
                              </div>

                              {/* Application */}
                              <div>
                                <AntInput
                                  placeholder="Application"
                                  value={editFormData.application}
                                  onChange={(e) =>
                                    setEditFormData({
                                      ...editFormData,
                                      application: e.target.value,
                                    })
                                  }
                                  className="w-full h-8 border border-input rounded-md"
                                  style={{ borderColor: "#e5e7eb" }}
                                />
                              </div>

                              {/* Customer's Procurement */}
                              <div className="flex">
                                <input
                                  type="number"
                                  min="0"
                                  placeholder="Annual volume"
                                  value={
                                    editFormData.procurement_volume !== null
                                      ? editFormData.procurement_volume
                                      : ""
                                  }
                                  onChange={(e) => {
                                    const value = e.target.value;
                                    setEditFormData({
                                      ...editFormData,
                                      procurement_volume:
                                        value === "" ? null : parseFloat(value),
                                    });
                                  }}
                                  style={{
                                    width: "50%",
                                    borderColor: "#e5e7eb",
                                    textOverflow: "ellipsis",
                                    whiteSpace: "nowrap",
                                    overflow: "hidden",

                                  }}
                                  className="h-8 border border-input rounded-l-md text-left placeholder:text-center placeholder:text-xs placeholder:text-gray-400 [appearance:textfield] [&::-webkit-outer-spin-button]:appearance-none [&::-webkit-inner-spin-button]:appearance-none"
                                />
                                <div className="w-[50%]">
                                  <LabeledSelect
                                    value={editFormData.procurement_unit || ""}
                                    onValueChange={(value) =>
                                      setEditFormData({
                                        ...editFormData,
                                        procurement_unit: value,
                                      })
                                    }
                                    placeholder="Unit"
                                    triggerClassName="h-8 bg-white rounded-l-none border-l-0"
                                  >
                                    <LabeledSelectItem
                                      value="Metric Ton (mt)"
                                      label="Mt"
                                    >
                                      Metric Ton (mt)
                                    </LabeledSelectItem>
                                    <LabeledSelectItem
                                      value="Pound (lb)"
                                      label="Lb"
                                    >
                                      Pound (lb)
                                    </LabeledSelectItem>
                                    <LabeledSelectItem
                                      value="Gallon (gal)"
                                      label="Gal"
                                    >
                                      Gallon (gal)
                                    </LabeledSelectItem>
                                    <LabeledSelectItem
                                      value="Litre (L)"
                                      label="L"
                                    >
                                      Litre (L)
                                    </LabeledSelectItem>
                                    <LabeledSelectItem
                                      value="Kilolitre (Kl)"
                                      label="Kl"
                                    >
                                      Kilolitre (Kl)
                                    </LabeledSelectItem>
                                    <LabeledSelectItem
                                      value="Kilogram (Kg)"
                                      label="Kg"
                                    >
                                      Kilogram (Kg)
                                    </LabeledSelectItem>
                                  </LabeledSelect>
                                </div>
                              </div>

                              {/* Expected Procurement */}
                              <div className="flex">
                                <input
                                  type="number"
                                  min="0"
                                  placeholder="Expected volume"
                                  value={
                                    editFormData.expected_procurement_volume !==
                                    null
                                      ? editFormData.expected_procurement_volume
                                      : ""
                                  }
                                  onChange={(e) => {
                                    const value = e.target.value;
                                    setEditFormData({
                                      ...editFormData,
                                      expected_procurement_volume:
                                        value === "" ? null : parseFloat(value),
                                    });
                                  }}
                                  style={{
                                    width: "50%",
                                    borderColor: "#e5e7eb",
                                    textOverflow: "ellipsis",
                                    whiteSpace: "nowrap",
                                    overflow: "hidden",

                                  }}
                                  className="h-8 border border-input rounded-l-md text-left placeholder:text-center placeholder:text-xs placeholder:text-gray-400 [appearance:textfield] [&::-webkit-outer-spin-button]:appearance-none [&::-webkit-inner-spin-button]:appearance-none"
                                />
                                <div className="w-[50%]">
                                  <LabeledSelect
                                    value={
                                      editFormData.expected_procurement_unit ||
                                      ""
                                    }
                                    onValueChange={(value) =>
                                      setEditFormData({
                                        ...editFormData,
                                        expected_procurement_unit: value,
                                      })
                                    }
                                    placeholder="Unit"
                                    triggerClassName="h-8 bg-white rounded-l-none border-l-0"
                                  >
                                    <LabeledSelectItem
                                      value="Metric Ton (mt)"
                                      label="Mt"
                                    >
                                      Metric Ton (mt)
                                    </LabeledSelectItem>
                                    <LabeledSelectItem
                                      value="Pound (lb)"
                                      label="Lb"
                                    >
                                      Pound (lb)
                                    </LabeledSelectItem>
                                    <LabeledSelectItem
                                      value="Gallon (gal)"
                                      label="Gal"
                                    >
                                      Gallon (gal)
                                    </LabeledSelectItem>
                                    <LabeledSelectItem
                                      value="Litre (L)"
                                      label="L"
                                    >
                                      Litre (L)
                                    </LabeledSelectItem>
                                    <LabeledSelectItem
                                      value="Kilolitre (Kl)"
                                      label="Kl"
                                    >
                                      Kilolitre (Kl)
                                    </LabeledSelectItem>
                                    <LabeledSelectItem
                                      value="Kilogram (Kg)"
                                      label="Kg"
                                    >
                                      Kilogram (Kg)
                                    </LabeledSelectItem>
                                  </LabeledSelect>
                                </div>
                              </div>

                              {/* Remarks */}
                              <div>
                                <AntInput
                                  placeholder="Remarks"
                                  value={editFormData.remarks}
                                  onChange={(e) =>
                                    setEditFormData({
                                      ...editFormData,
                                      remarks: e.target.value,
                                    })
                                  }
                                  className="w-full h-8 border border-input rounded-md"
                                  style={{ borderColor: "#e5e7eb" }}
                                />
                              </div>
                            </div>
                          </div>
                        </div>

                        {/* Action Buttons */}
                        <div className="flex justify-end gap-2">
                          <Button
                            variant="outline"
                            size="sm"
                            className="flex items-center border-gray-300 hover:bg-gray-50"
                            onClick={() => {
                              setIsEditing(false);
                              refetchAll();
                            }}
                          >
                            <X className="h-4 w-4 mr-1 text-gray-500 font-bold stroke-[2.5px]" />
                            <span className="text-gray-500 font-medium">
                              Cancel
                            </span>
                          </Button>
                          <Button
                            variant="outline"
                            size="sm"
                            className="flex items-center border-[#294d48] hover:bg-[#294d48]/5"
                            onClick={async () => {
                              if (!selectedEnquiry?.id) return;
                            // Validate mandatory fields
                            const mandatoryFields = [
                              { key: "Chemical Name", label: "Chemical Name", value: editFormData.chemical_name },
                              { key: "Quantity", label: "Quantity", value: editFormData.quantity },
                              { key: "Incoterms", label: "Incoterms", value: editFormData.incoterms },
                              { key: "Destination", label: "Destination", value: editFormData.destination },
                              { key: "Confidence", label: "Criticality", value: editFormData.confidence },
                            ];
                            
                            const missingFields = mandatoryFields.filter(f => !f.value).map(f => f.key);
                            
                            if (missingFields.length > 0) {
                              setInvalidFields(missingFields);
                              setShowValidationErrors(true);
                            
                              toast.error(`Please fill all mandatory fields: ${missingFields.join(', ')}`, {
                                position: 'bottom-right',
                                duration: 3000,
                                style: {
                                  fontSize: '15px',              
                                  padding: '12px 16px',         
                                  minWidth: '300px',             
                                  background: 'white',
                                  border: '1px solid #d1d5db',
                                  borderRadius: '6px',           
                                  boxShadow: '0 2px 4px rgba(0, 0, 0, 0.08)',
                                }
                              });
                            
                              return;
                            }
                            setShowValidationErrors(false);
                            setInvalidFields([]);
                            
                              try {
                                // Update the enquiry with all the form data
                                const { error: enquiryError } = await supabase
                                  .from("enquiries")
                                  .update({
                                    chemical_name: editFormData.chemical_name,
                                    incoterms: editFormData.incoterms,
                                    quantity: editFormData.quantity,
                                    quantity_unit:
                                      editFormData.quantity_unit as
                                        | "Metric Ton (mt)"
                                        | "Pound (lb)"
                                        | "Gallon (gal)"
                                        | "Litre (L)"
                                        | "Kilolitre (Kl)"
                                        | "Kilogram (Kg)",
                                    destination: editFormData.destination,
                                    cas_number: editFormData.cas_number,
                                    confidence: editFormData.confidence,
                                    application: editFormData.application,
                                    remarks: editFormData.remarks,
                                    target_price: editFormData.target_price,
                                    packaging_type: editFormData.packaging_type,
                                    qty_per_packaging: editFormData.qty_per_packaging,
                                    target_price_currency:
                                      editFormData.target_price_currency,
                                    procurement_volume:
                                      editFormData.procurement_volume,
                                    procurement_unit:
                                      editFormData.procurement_unit,
                                    expected_procurement_volume:
                                      editFormData.expected_procurement_volume,
                                    expected_procurement_unit:
                                      editFormData.expected_procurement_unit,
                                      qty_per_packaging_unit: editFormData.qty_per_packaging_unit,
                                    updated_on: new Date().toISOString(),
                                  })
                                  .eq("id", selectedEnquiry.id);

                                if (enquiryError) throw enquiryError;

                                // Upload documents if any
                                if (editFormData.attachedFiles.length > 0) {
                                  await uploadDocuments(
                                    editFormData.attachedFiles,
                                    selectedEnquiry.id
                                  );
                                }

                                // Refetch data to update the UI
                                refetchAll();
                                setIsEditing(false);
                                toast.success("Enquiry updated successfully");
                              } catch (error) {
                                console.error("Error updating enquiry:", error);
                                toast.error("Failed to update enquiry");
                              }
                            }}
                          >
                            <Check className="h-4 w-4 mr-1 text-[#294d48] font-bold stroke-[2.5px]" />
                            <span className="bg-gradient-to-r from-[#294d48] via-[#294d48] to-[#294d48]/80 bg-clip-text text-transparent font-medium">
                              Save
                            </span>
                          </Button>
                        </div>
                      </div>
                    ) : null}
                  </div>
                </div>
              </>
            ) : null}
          </div>

          {/* Enquiry Details Table - Always visible above tabs */}
          <div className="px-4 pt-2">
            {selectedEnquiry &&
              !isCancelling &&
              (isEditing ? null : ( // Keep the existing edit form when in edit mode
                <EnquiryDetailsTable enquiry={selectedEnquiry} />
              ))}
          </div>

          {/* Tabs navigation */}
          <div className="px-4 flex-grow flex flex-col overflow-hidden">
            <Tabs defaultValue="details" value={activeTab} onValueChange={setActiveTab} className="w-full flex-grow flex flex-col overflow-hidden">
              <TabsList className={`grid w-full ${hasSampleRequests ? 'grid-cols-2' : 'grid-cols-1'} mb-4 flex-shrink-0`}>
                <TabsTrigger value="details" className="flex items-center gap-1 data-[state=active]:bg-[#294d48]/10 data-[state=active]:text-[#294d48] data-[state=active]:shadow-none">
                  <Info className="h-4 w-4" />
                  <span>Enquiry Details</span>
                </TabsTrigger>
                {hasSampleRequests && (
                  <TabsTrigger value="timeline" className="flex items-center gap-1 data-[state=active]:bg-[#294d48]/10 data-[state=active]:text-[#294d48] data-[state=active]:shadow-none">
                    <Beaker className="h-4 w-4" />
                    <span>Sample Details</span>
                  </TabsTrigger>
                )}
              </TabsList>

              {/* Scrollable content area - takes remaining height */}
              <div className="flex-grow overflow-hidden relative">
                <TabsContent value="details" className="mt-0 absolute inset-0 overflow-y-auto">
                  <div className="pb-6">
                    <div className="space-y-6">
                      {/* Follow-up Scheduled */}
                      {hasFollowUp && (
                        <div className="timeline-item">
                          <div className="timeline-icon">
                            <div className="timeline-icon-circle bg-blue-50">
                              <Clock className="h-4 w-4 text-blue-600" />
                            </div>
                            <div className="timeline-line"></div>
                          </div>
                          <div className="timeline-content">
                            <div className="timeline-title">
                              <h4 className="text-base font-medium">
                                Follow-up Scheduled
                              </h4>
                              <span className="text-sm text-gray-500">
                                08/04/2025
                              </span>
                            </div>
                            <p className="text-sm text-gray-600 mt-1">
                              Follow up with customer scheduled for 11/04/2025
                            </p>
                          </div>
                        </div>
                      )}

                      {/* Follow-up Scheduled */}
                      {hasFollowUp && (
                        <div className="mb-6">
                          <FollowUpScheduledItem
                            item={{
                              follow_up_date: "2023-11-04", // This would come from your data
                              notes:
                                "Follow up with customer scheduled for 11/04/2023",
                            }}
                            formatTimestamp={formatTimestamp}
                          />
                        </div>
                      )}

                      {/* Map through all status history items except sample-related ones */}
                      {processedStatusHistory.map((item, index) => {
                        // Skip certain statuses that should not be shown
                        if (
                          [
                            "quote_accepted",
                            "sample_redo",
                            "sample_accepted",
                            "sample_requested",
                            "sample_available",
                            "sample_in_transit",
                            "sample_delivered",
                          ].includes(item.status)
                        ) {
                          return null;
                        }

                        // Render the appropriate component based on status
                        if (item.status === "enquiry_created") {
                          return (
                            <div className="mb-4" key={item.id || `status-${index}`}>
                              <EnquiryCreatedItem
                                item={item}
                                formatTimestamp={formatTimestamp}
                                formatTime={formatTime}
                              />
                            </div>
                          );
                        }

                        if (item.status === "enquiry_assigned") {
                          return (
                            <div className="mb-4" key={item.id || `status-${index}`}>
                              <EnquiryAssignedItem
                                item={item}
                                formatTimestamp={formatTimestamp}
                                formatTime={formatTime}
                              />
                            </div>
                          );
                        }

                        if (item.status === "pricing_quotation_generated") {
                          console.log(
                            "Rendering PricingQuotationGeneratedItem:",
                            item
                          );
                          return (
                            <div
                              className="mb-4"
                              key={item.id || `status-${index}`}
                            >
                              <PricingQuotationGeneratedItem
                                item={item}
                                enquiryId={enquiryId || ""}
                                enquiryStatusId={item.id}
                                formatTimestamp={formatTimestamp}
                                formatTime={formatTime}
                                quotationFeedback={quotationFeedback}
                                onRefresh={refetchAll}
                                currentStatus={selectedEnquiry?.current_status}
                                handleRequestNewQuotation={
                                  handleRequestNewQuotation
                                }
                              />
                            </div>
                          );
                        }

                        if (item.status === "clarification_needed") {
                          return (
                            <div className="mb-4" key={item.id || `status-${index}`}>
                              <ClarificationNeededItem
                                item={item}
                                enquiryId={enquiryId || ""}
                                formatTimestamp={formatTimestamp}
                                formatTime={formatTime}
                                onRefresh={refetchAll}
                              />
                            </div>
                          );
                        }

                        if (item.status === "po_raised") {
                          return (
                            <div className="mb-4" key={item.id || `status-${index}`}>
                              <PORaisedItem
                                item={item}
                                enquiryId={enquiryId || ""}
                                formatTimestamp={formatTimestamp}
                                formatTime={formatTime}
                                onRefresh={refetchAll}
                                handleRequestNewQuotation={handleRequestNewQuotation}
                                showRequestButton={showRequestButton}
                                index={index}
                              />
                            </div>
                          );
                        }

                        // For other status types, use a generic component
                        return (
                            <div className="mb-4" key={item.id || `status-${index}`}>
                            <div className="flex gap-4">
                              <div className="flex-none">
                              <div className="w-8 h-8 rounded-full bg-gray-100 flex items-center justify-center">
                                <FileText className="h-4 w-4 text-gray-600" />
                              </div>
                              </div>
                              <div className="flex-grow">
                              <div className="flex justify-between">
                                <p className="text-base font-bold mb-0">
                                {item.status
                                  .replace(/_/g, " ")
                                  .replace(/\b\w/g, (l) => l.toUpperCase())}
                                </p>
                                <span className="text-sm text-gray-500">
                                {formatTimestamp(item.created_at)}{" "}
                                {formatTime(item.created_at)}
                                </span>
                              </div>
                              {item.notes && !(item.status === "quote_revision_needed" || item.status === "quote_redo") && (
                                <p className="text-sm text-gray-600 mt-1">
                                {item.notes}
                                </p>
                              )}
                              {(item.status === "quote_revision_needed" || item.status === "quote_redo") && (
                                <span className="flex gap-1 text-sm text-gray-600">
                                <p>{item.notes}</p>
                                {item.procurement_poc && (
                                  <>
                                  <span>|</span>
                                  <span>Assigned to: <span className="font-bold">{item.procurement_poc}</span></span>
                                  </>
                                )}
                                </span>
                              )}
                              </div>
                            </div>
                            </div>
                        );
                      })}
                    </div>
                  </div>
                </TabsContent>

                {hasSampleRequests && (
                  <TabsContent value="timeline" className="mt-0 absolute inset-0 overflow-y-auto">
                    <div className="pb-6">
                      <SampleRequestsList
                        sampleRequests={sampleRequests}
                        enquiryId={enquiryId || ""}
                        formatTimestamp={formatTimestamp}
                        formatTime={formatTime}
                        onRefresh={refetchAll}
                        onRequestSample={() => {
                          setShowSampleRequestForm(true);
                          setActiveTab("details");
                        }}
                      />
                    </div>
                  </TabsContent>
                )}
              </div>
            </Tabs>
          </div>

          {/* Documents Modal */}
          {enquiryId && (
            <EnquiryDocumentsModal
              isOpen={isDocumentsModalOpen}
              setIsOpen={setIsDocumentsModalOpen}
              enquiryId={enquiryId}
            />
          )}
        </DialogContent>
      </Dialog>
    </>
  );
};

export default EnquiryLifecycleDialog;
