import React, { useState, use<PERSON><PERSON>back, useMemo, useEffect } from "react";
import { Card } from "@/components/ui/card";
import EnquiryTableView from "./EnquiryTableView";
import EnquiryMiniCard from "./EnquiryMiniCard";
import {
  useEnquiriesData,
  EnquiryType,
} from "../enquiries/hooks/useEnquiriesData";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Badge } from "@/components/ui/badge";
import { Layers, Search, Loader2} from "lucide-react";
import { Input } from "@/components/ui/input";
import EnquiryLifecycleDialog from "../enquiries/EnquiryLifecycleDialog";
import { useKanbanSubscription } from "./hooks/useKanbanSubscription";
import { useStatsSubscription } from "../hooks/useStatsSubscription";
import { useQuery } from "@tanstack/react-query";
import { supabase } from "@/integrations/supabase/client";
import { Select as AntSelect } from "antd";
import { SALES_TEAM_MAPPING, CATEGORIES } from '@/config/salesTeamMapping';
import { toast } from "sonner";
import { ChevronDown, ChevronUp } from "lucide-react";
import { Button } from "@/components/ui/button";
import * as XLSX from "xlsx"
import axios from "axios";
import { API_CONFIG } from '@/config/api';
import { EyeIcon } from "lucide-react";

// Define the stage type
type KanbanStage = {
  id: string;
  name: string;
  states: string[];
  description?: string;
  subsections?: {
    id: string;
    name: string;
    states: string[];
  }[];
};

// Define the 5 main stages and their corresponding states
const KANBAN_STAGES: KanbanStage[] = [
  {
    id: "open_enquiries",
    name: "Open Enquiries",
    states: [
      "enquiry_created",
      "enquiry_assigned",
      "clarification_needed",
      "quote_redo",
      "quote_revision_needed",
    ],
    description: "New and in-progress enquiries",
    subsections: [
      {
        id: "clarification_needed",
        name: "Clarification Needed",
        states: ["clarification_needed"],
      },
      {
        id: "enquiry_assigned",
        name: "Pending With Sourcing",
        states: [
          "enquiry_created",
          "enquiry_assigned",
          "quote_redo",
          "quote_revision_needed"
        ],
      },
    ],
  },
  {
    id: "pricing_quotation",
    name: "Quotes Generated",
    states: ["pricing_quotation_generated", "quote_accepted"],
    description: "Quotations ready for review",
    subsections: [
      {
        id: "without_feedback",
        name: "No customer feedback",
        states: ["pricing_quotation_generated"]
      },
      {
        id: "quote_accepted",
        name: "Quotes Accepted",
        states: ["quote_accepted"]
      }
    ]
  },
  {
    id: "open_samples",
    name: "Open Samples",
    states: [
      "sample_requested",
      "sample_available",
      "sample_in_transit",
      "sample_redo",
    ],
    description: "Samples in preparation or transit",
  },
  {
    id: "sample_delivered",
    name: "Samples Delivered",
    states: ["sample_delivered", "sample_accepted", "sample_redo"],
    description: "Samples delivered to customer",
    subsections: [
      {
        id: "without_feedback",
        name: "No customer feedback",
        states: ["sample_delivered"]
      },
      {
        id: "samples_accepted",
        name: "Samples Accepted",
        states: ["sample_accepted", "sample_redo"]
      }
    ]
  },
  {
    id: "po_raised",
    name: "POs Raised",
    states: ["po_raised"],
    description: "Purchase orders confirmed",
  },
];

// No need for COLUMNS_PER_ROW anymore

const EnquiryKanban = () => {
  // Get role and category from localStorage
  const userRole = localStorage.getItem("userRole");
  const userCategory = localStorage.getItem("userCategory");

  const [selectedStage, setSelectedStage] = useState<string | null>(null);
  const [tableViewStage, setTableViewStage] = useState<string | null>(null);
  const [searchQuery, setSearchQuery] = useState("");
  const [selectedEnquiry, setSelectedEnquiry] = useState<EnquiryType | null>(
    null
  );
  const [selectedCategory, setSelectedCategory] = useState<string>(null);
  const [selectedSalesTeamMember, setSelectedSalesTeamMember] =
    useState<string>("all");
  const [salesTeamMember,setSalesTeamMembers] = useState<any[]>([]);
  const SALES_TEAM_MAPPING = salesTeamMember || []
  const [loading,setLoaidng] = useState<boolean>(false);

  // Add useEffect to handle category initialization and updates
  useEffect(() => {
    const currentUserRole = localStorage.getItem("userRole");
    const currentUserCategory = localStorage.getItem("userCategory");
    
    if (currentUserRole === 'bu_head' && currentUserCategory) {
      setSelectedCategory(currentUserCategory);
    } else if(currentUserRole === 'admin') {
      setSelectedCategory('all');
    } 
  }, [localStorage.getItem("userRole"), localStorage.getItem("userCategory")]); // Empty dependency array means this runs once on mount

  // Fetch sales team members from the database
  useEffect(() => {
    const fetchSalesTeam = async () => {
      try {
        const { data, error } = await supabase
          .from("user_list")
          .select(`
            id,
            email,
            user_roles!inner(role, category)
          `)
          .order("email");

        if (error) {
          console.error("Error fetching sales team:", error);
          toast.error("Failed to load sales team members");
          return;
        }

        // Transform data to match the format needed for the dropdown
        const formattedTeam = data.map((member) => {
          // Ensure we have the user_roles data
          const userRole = member.user_roles?.[0];

          return {
            value: member.email,
            label: member.email.split("@")[0], // Show only the part before @
            category: userRole?.category || null
          };
        });

        // Filter out members with null category
        const validMembers = formattedTeam.filter(
          (member) => member.category !== null && member.category !== undefined
        );

        setSalesTeamMembers(validMembers);
      } catch (error) {
        console.error("Exception fetching sales team:", error);
        toast.error("Failed to load sales team members");
      }
    };

    fetchSalesTeam();
  }, []);


  const {
    data: enquiries,
    isLoading,
    refetch,
  } = useEnquiriesData(
    "total",
    "desc",
    userRole || "", // Pass the role from localStorage
    userCategory || "" // Pass the category from localStorage
  );
  // We no longer need to fetch status history here as it's handled in the EnquiryLifecycleDialog

  // Setup the callback for real-time updates
  const handleDataUpdate = useCallback(() => {
    refetch();
  }, [refetch]);

  // Use our custom subscription hook
  useKanbanSubscription(handleDataUpdate);

  const { data: session } = useQuery({
    queryKey: ["auth-session"],
    queryFn: async () => {
      const {
        data: { session },
      } = await supabase.auth.getSession();
      return session;
    },
  });

  // Setup real-time subscriptions
  useStatsSubscription(session?.user?.email, handleDataUpdate);

  // Get filtered options for sales team dropdown based on selected category
  const salesTeamOptions = useMemo(() => {

    if (!selectedCategory || selectedCategory === "all") {
      return [
        { value: "all", label: "All Members" },
        ...SALES_TEAM_MAPPING.map(member => ({
          value: member.value,
          label: member.label,
        }))
      ];
    }

    const filteredMembers = SALES_TEAM_MAPPING
      .filter(member => member.category === selectedCategory)
      .map(member => ({
        value: member.value,
        label: member.label,
      }));

    return [
      { value: "all", label: "All Members" },
      ...filteredMembers
    ];
  }, [selectedCategory, SALES_TEAM_MAPPING]);

  const filteredEnquiries = useMemo(() => {
    if (!enquiries) return [];

    let filtered = [...enquiries];

    // Apply category filter
    if (selectedCategory && selectedCategory !== "all") {

      const teamMembersInCategory = SALES_TEAM_MAPPING
        .filter(member => member.category === selectedCategory)
        .map(member => member.value.toLowerCase());

      filtered = filtered.filter(enquiry =>
        teamMembersInCategory.includes(enquiry.sales_team_member?.toLowerCase())
      );
    }

    // Apply sales team member filter
    if (selectedSalesTeamMember && selectedSalesTeamMember !== "all") {
      filtered = filtered.filter(enquiry =>
        enquiry.sales_team_member?.toLowerCase() === selectedSalesTeamMember.toLowerCase()
      );
    }

    // Apply search query filter if exists
    if (searchQuery.trim()) {
      const query = searchQuery.toLowerCase().trim();
      filtered = filtered.filter((enquiry) => {
        const customerName = (enquiry.customer_full_name || "").toLowerCase();
        const productName = (enquiry.chemical_name || enquiry.product || "").toLowerCase();
        const casNumber = (enquiry.cas_number || "").toLowerCase();
        const enquiryId = (enquiry.enquiry_id || "").toLowerCase();
        const salesTeamMember = SALES_TEAM_MAPPING.find(
          member => member.value.toLowerCase() === enquiry.sales_team_member?.toLowerCase()
        );
        const salesTeamMemberName = salesTeamMember?.label?.toLowerCase() || "";
        const salesTeamMemberCountry = salesTeamMember?.country?.toLowerCase() || "";

        // Handle ID search with improved logic
        let searchId = query;
        if (searchId.startsWith("enq-")) {
          searchId = searchId.substring(4); // Remove "enq-" prefix
        }

        // Get the first 5 chars of the UUID for comparison
        const idFirstFiveChars = enquiry.id
          ? enquiry.id.toString().toLowerCase().substring(0, 5)
          : "";

        return (
          customerName.includes(query) ||
          productName.includes(query) ||
          casNumber.includes(query) ||
          enquiryId?.toLowerCase().includes(query) ||
          idFirstFiveChars.includes(searchId) ||
          salesTeamMemberName.includes(query) ||
          salesTeamMemberCountry.includes(query)
        );
      });
    }

    return filtered;
  }, [enquiries, searchQuery, selectedCategory, selectedSalesTeamMember]);

  // Group enquiries by their stage based on current status
  const groupedEnquiries = React.useMemo(() => {
    if (!filteredEnquiries) return {};

    // First, create a mapping from status to stage
    const statusToStage: Record<string, string> = {};
    KANBAN_STAGES.forEach((stage) => {
      stage.states.forEach((status) => {
        statusToStage[status] = stage.id;
      });
    });
   
    console.log("Status to Stage Mapping:", filteredEnquiries);

    // Group enquiries by stage
    return filteredEnquiries.reduce<Record<string, EnquiryType[]>>(
      (acc, enquiry) => {
        const status = enquiry.current_status;
        const stageId = statusToStage[status] || "other";

        if (!acc[stageId]) {
          acc[stageId] = [];
        }
        acc[stageId].push(enquiry);
        return acc;
      },
      {}
    );
  }, [filteredEnquiries]);

  // Create a single row with all stages
  const stageRows = React.useMemo(() => {
    return [KANBAN_STAGES.map((stage) => stage.id)];
  }, []);

  const handleEnquiryClick = (enquiry: EnquiryType) => {
    console.log("Enquiry clicked:", enquiry);
    setSelectedEnquiry(enquiry);
    sessionStorage.setItem('openEnquiryId', enquiry?.enquiry_id);
    sessionStorage.setItem('dialogAlreadyOpened', 'true');

  };

  const handleCloseDialog = () => {
    setSelectedEnquiry(null);
    sessionStorage.removeItem('openEnquiryId');
    sessionStorage.removeItem('dialogAlreadyOpened');
  };

  // Find the current stage object if in table view
  const currentStage = tableViewStage
    ? KANBAN_STAGES.find((s) => s.id === tableViewStage)
    : null;

  // Get all enquiries for the current stage
  const stageEnquiries = React.useMemo(() => {
    if (!tableViewStage || !groupedEnquiries[tableViewStage]) return [];
    return groupedEnquiries[tableViewStage];
  }, [tableViewStage, groupedEnquiries]);

  useEffect(() => {
    if (!enquiries || enquiries.length === 0) return;
    // Skip if we already have a selected enquiry
    if (selectedEnquiry) return;
    
    // Check for stored ID in session storage
    const openEnquiryId = sessionStorage.getItem('openEnquiryId');
    if (!openEnquiryId) return;
    
    // Check if dialog is already opened to prevent duplicate openings
    const dialogAlreadyOpened = sessionStorage.getItem('dialogAlreadyOpened');
    
    // Find the enquiry in the loaded data
    const foundEnquiry = enquiries.find(enquiry => 
      enquiry.id === openEnquiryId || 
      enquiry.enquiry_id === openEnquiryId
    );
    
    if (foundEnquiry && !dialogAlreadyOpened) {
      console.log("Found stored enquiry to open:", foundEnquiry);
      
      // Set the flag to prevent other cards from opening
      sessionStorage.setItem('dialogAlreadyOpened', 'true');
      
      // Set the selected enquiry
      setSelectedEnquiry(foundEnquiry);
      
    }
  }, [enquiries, selectedEnquiry]);

  if (isLoading) {
    return (
      <div className="py-8">
        <Card className="p-6 bg-white/70 backdrop-blur-sm">
          <div className="flex items-center justify-center h-40">
            <p className="text-gray-500">Loading enquiries...</p>
          </div>
        </Card>
      </div>
    );
  }

  // Alternative simpler format (just date without time)
const formatDateOnlyForExport = (dateString: string | null | undefined): string => {
  if (!dateString || dateString === "" || dateString === null || dateString === undefined) {
    return "";
  }

  try {
    const date = new Date(dateString);
    
    if (isNaN(date.getTime())) {
      return "";
    }

    // Format as: DD/MM/YYYY
    return date.toLocaleDateString('en-GB', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric'
    });
  } catch (error) {
    console.error("Error formatting date:", dateString, error);
    return "";
  }
};

// Add these helper functions after the date formatting functions
const formatPriceWithUnit = (price: number | null, unit: string | null, currency: string | null): string => {
  if (!price && price !== 0) {
    return "";
  }
  
  let formattedPrice = price.toString();
  
  // Add currency if available
  if (currency) {
    formattedPrice = `${formattedPrice} ${currency}`;
  }
  
  // Add unit if available
  if (unit) {
    formattedPrice = `${formattedPrice}/${unit}`;
  }
  
  return formattedPrice;
};


const handleExportData = async () => {
  try {
    // 1. Get export data from the function
    setLoaidng(true);
    const response = await axios.get(
      `${API_CONFIG.catalogBaseUrl}get-export-data`,
      {
        headers: {
          accept: "application/json",
          Authorization: `Bearer ${API_CONFIG.accessToken}`,
        },
      }
    );

    if (!response.data) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const exportData = response.data;

    // 2. Extract PO numbers and create CSV
    const poNumbers = exportData
      .filter((item) => item.poNumber && item.poNumber !== "N/A")
      .map((item) => item.poNumber);

    const uniquePoNumbers = [...new Set(poNumbers)]; // Remove duplicates
    const poNumbersCSV = uniquePoNumbers.join(",");

    // 3. Get delivery dates for PO numbers
    let deliveryData = [];
    if (poNumbersCSV) {
      const deliveryResponse = await axios.post(
        `${API_CONFIG.baseUrl}/${API_CONFIG.endpoints.purchaseOrderDetails}`,
        {
          parameters: [
            {
              type: "category",
              target: ["variable", ["template-tag", "po_numbers_csv"]],
              value: poNumbersCSV,
            },
          ],
        },
        {
          headers: {
            Authorization: `Bearer ${API_CONFIG.accessToken}`,
            "Content-Type": "application/json",
          },
        }
      );

      deliveryData = deliveryResponse.data || [];
      console.log("Delivery data:", deliveryData);
    }

    // 4. Process and merge data
    const processedData = [];

    exportData.forEach((exportItem) => {
      const matchingDeliveries = deliveryData.filter(
        (delivery) => delivery.purchase_order_number === exportItem.poNumber
      );
         const { targetPriceUnit, targetPriceCurrency, quotedPriceUnit, quotedPriceCurrency, ...cleanedItem } = exportItem;

      if (matchingDeliveries.length === 0) {
        // No delivery data found, add original item
        processedData.push({
          ...cleanedItem,
          enquiryDate: formatDateOnlyForExport(exportItem.enquiryDate),
          lastQuoteStatusUpdateDate: formatDateOnlyForExport(
            exportItem.lastQuoteStatusUpdateDate
          ),
          lastSampleStatusUpdateDate: formatDateOnlyForExport(
            exportItem.lastSampleStatusUpdateDate
          ),
          poDate: formatDateOnlyForExport(exportItem.poDate),
          deliveryDate: formatDateOnlyForExport(exportItem.deliveryDate),
          targetPrice: formatPriceWithUnit(
            exportItem.targetPrice,
            exportItem.targetPriceUnit,
            exportItem.targetPriceCurrency
          ),
          quotedPrice: formatPriceWithUnit(
            exportItem.quotedPrice,
            exportItem.quotedPriceUnit,
            exportItem.quotedPriceCurrency
          ),
        });
      } else if (matchingDeliveries.length === 1) {
        // Single delivery, update the item
        processedData.push({
          ...cleanedItem,
          CDOchemicalName:
            matchingDeliveries[0].chemical_name || exportItem.chemicalName,
          enquiryDate: formatDateOnlyForExport(exportItem.enquiryDate),
          lastQuoteStatusUpdateDate: formatDateOnlyForExport(
            exportItem.lastQuoteStatusUpdateDate
          ),
          lastSampleStatusUpdateDate: formatDateOnlyForExport(
            exportItem.lastSampleStatusUpdateDate
          ),
          poDate: formatDateOnlyForExport(exportItem.poDate),
          deliveryDate: formatDateOnlyForExport(matchingDeliveries[0].delivery_date),
          targetPrice: formatPriceWithUnit(
            exportItem.targetPrice,
            exportItem.targetPriceUnit,
            exportItem.targetPriceCurrency
          ),
          quotedPrice: formatPriceWithUnit(
            exportItem.quotedPrice,
            exportItem.quotedPriceUnit,
            exportItem.quotedPriceCurrency
          ),
        });
      } else {
        // Multiple deliveries, duplicate the item for each delivery
        matchingDeliveries.forEach((delivery) => {
          processedData.push({
            ...cleanedItem,
            CDOchemicalName: delivery.chemical_name || exportItem.chemicalName,
            enquiryDate: formatDateOnlyForExport(exportItem.enquiryDate),
            lastQuoteStatusUpdateDate: formatDateOnlyForExport(
              exportItem.lastQuoteStatusUpdateDate
            ),
            lastSampleStatusUpdateDate: formatDateOnlyForExport(
              exportItem.lastSampleStatusUpdateDate
            ),
            poDate: formatDateOnlyForExport(exportItem.poDate),
            deliveryDate: formatDateOnlyForExport(delivery.delivery_date),
            targetPrice: formatPriceWithUnit(
              exportItem.targetPrice,
              exportItem.targetPriceUnit,
              exportItem.targetPriceCurrency
            ),
            quotedPrice: formatPriceWithUnit(
              exportItem.quotedPrice,
              exportItem.quotedPriceUnit,
              exportItem.quotedPriceCurrency
            ),
          });
        });
      }
    });

    console.log("Processed data:", processedData);

    // 5. Create Excel file
    const worksheet = XLSX.utils.json_to_sheet(processedData);

    // Style the headers - make them bold and larger
    const range = XLSX.utils.decode_range(worksheet["!ref"] || "A1");
    for (let col = range.s.c; col <= range.e.c; col++) {
      const cellAddress = XLSX.utils.encode_cell({ r: 0, c: col }); // First row (headers)
      if (worksheet[cellAddress]) {
        worksheet[cellAddress].s = {
          font: {
            bold: true,
            sz: 16, // Font size (default is usually 11)
            color: { rgb: "000000" }, // Black color
          },
          fill: {
            fgColor: { rgb: "E6E6E6" }, // Light gray background
          },
          alignment: {
            horizontal: "center",
            vertical: "center",
          },
          border: {
            top: { style: "thin", color: { rgb: "000000" } },
            bottom: { style: "thin", color: { rgb: "000000" } },
            left: { style: "thin", color: { rgb: "000000" } },
            right: { style: "thin", color: { rgb: "000000" } },
          },
        };
      }
    }

    // Set column widths for better readability
    const columnWidths = [
      { wch: 40 }, // salesPOC
      { wch: 25 }, // customerName
      { wch: 15 }, // category
      { wch: 20 }, // uniqueNumber
      { wch: 30 }, // enquiryDate
      { wch: 30 }, // chemicalName
      { wch: 12 }, // criticality
      { wch: 30 }, // quotationStatus
      { wch: 30 }, // lastQuoteStatusUpdateDate
      { wch: 25 }, // targetPrice
      { wch: 25 }, // quarterlyVolumeRequired
      { wch: 25 }, // quarterlyVolumeExpected
      { wch: 20 }, // numberOfPOs
      { wch: 25 }, //quoted price
      { wch: 25 }, // sampleStatus
      { wch: 30 }, // lastSampleStatusUpdateDat
      { wch: 30 }, // poDate
      { wch: 15 }, // poValue
      { wch: 15 }, // poNumber
      { wch: 12 }, // deliveryDate
      { wch: 30 }, // orderTag
      { wch: 30 }, // CDOChemicalName
    ];

    worksheet["!cols"] = columnWidths;

    const workbook = XLSX.utils.book_new();
    XLSX.utils.book_append_sheet(workbook, worksheet, "Export Data");
    XLSX.writeFile(workbook, "export_data_with_delivery.xlsx");

    setLoaidng(false);

    toast.success("Data exported successfully!");
  } catch (err) {
    const errorMessage =
      err instanceof Error ? err.message : "Failed to export data";
    console.error("Export error:", err);
    toast.error("Failed to export data: " + errorMessage);
  }
};


  return (
    <div>
      <Card className="p-6 bg-white/70 backdrop-blur-sm">
        {tableViewStage ? (
          <EnquiryTableView
            stageName={tableViewStage === 'all' ? 'All Enquiries' : currentStage?.name}
            enquiries={tableViewStage === 'all' ? filteredEnquiries : stageEnquiries}
            onBack={() => {
              setTableViewStage(null);
              setSelectedStage(null); // Reset the selected stage when going back to pipeline view
            }}
            onEnquiryClick={handleEnquiryClick}
          />
        ) : (
          // Kanban view
          <>
            <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-4 mb-6">
              <div className="flex items-center gap-2">
                <Layers className="h-5 w-5 text-[#294d48]" />
                <h2 className="text-xl font-semibold text-[#294d48] mb-0">
                  Enquiry Pipeline
                </h2>
                <Button
                  className="ml-4"
                  size="sm"
                  onClick={() => {
                    setTableViewStage('all');
                    setSelectedStage(null);
                  }}
                >
                  <EyeIcon className="h-4 w-4" />
                  View All Enquiries
                </Button>
                {userRole === 'admin' && 
                  <Button 
                    onClick={handleExportData} 
                    className="ml-4" 
                    size="sm"
                    disabled={loading}
                  >
                    {loading ? 
                      <Loader2 className="h-4 w-4 animate-spin mr-2" /> : 
                      "Export Enquiry Data"
                    }
                  </Button>
                }

              </div>

              <div className="flex items-center gap-4">
                {(userRole === 'bu_head' || userRole === 'admin') && selectedCategory !== null && (
                  <>
                    <AntSelect
                      value={selectedCategory || "all"}
                      onChange={(value) => {
                        setSelectedCategory(value);
                        setSelectedSalesTeamMember("all");
                      }}
                      style={{ width: 200, height: 40 }}
                      placeholder="Select category"
                      options={CATEGORIES}
                      className="bg-white h-10"
                      disabled={userRole === 'bu_head'}
                    />

                    <AntSelect
                      value={selectedSalesTeamMember}
                      showSearch
                      onChange={setSelectedSalesTeamMember}
                      style={{ width: 250, height: 40 }}
                      placeholder="Sales team member"
                      options={salesTeamOptions}
                      className="bg-white h-10"
                      filterOption={(input, option) =>
                        option?.label.toLowerCase().includes(input.toLowerCase())
                      }
                    />
                  </>
                )}
                <div className="relative max-w-xs w-full">
                  <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-gray-500" />
                  <Input
                    type="text"
                    placeholder="Search enquiries..."
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                    className="pl-9 bg-white border-[#D8DCDB] focus-visible:ring-[#294d48]"
                  />
                </div>
              </div>
            </div>

            <div className="space-y-6">
              {stageRows.map((row, rowIndex) => (
                <div
                  key={`row-${rowIndex}`}
                  className="pb-4 flex justify-between w-full"
                >
                  <div className="flex justify-between w-full space-x-4">
                    {row.map((stageId) => {
                      const stage = KANBAN_STAGES.find((s) => s.id === stageId);
                      if (!stage) return null;

                      const hasEnquiries =
                        groupedEnquiries[stageId]?.length > 0;
                      const count = groupedEnquiries[stageId]?.length || 0;
                      const isSelected = stageId === selectedStage;

                      return (
                        <div
                          key={stageId}
                          className="flex-1 min-w-0 max-w-[19%]"
                        >
                          <div
                            className={`p-3 rounded-t-lg transition-all duration-200 cursor-pointer border ${
                              isSelected
                                ? "bg-[#E6EAE9] border-[#294d48] border-b-0 shadow-sm"
                                : hasEnquiries
                                ? "bg-white/80 hover:bg-[#E6EAE9] border-[#D8DCDB] hover:border-[#294d48] border-b-0 hover:shadow-sm"
                                : "bg-gray-50/80 border-gray-200 hover:bg-gray-100/80 border-b-0"
                            }`}
                            onClick={() => {
                              setSelectedStage(isSelected ? null : stageId);
                              setTableViewStage(stageId);
                            }}
                          >
                            <div className="flex justify-between items-center">
                              <span
                                className={`font-medium break-words text-sm ${
                                  isSelected
                                    ? "text-[#294d48]"
                                    : "text-gray-700"
                                }`}
                              >
                                {stage.name}
                              </span>
                              <Badge
                                variant={hasEnquiries ? "default" : "outline"}
                                className={`${
                                  hasEnquiries
                                    ? "bg-[#294d48] hover:bg-[#294d48]/80"
                                    : "bg-transparent text-gray-400 border-gray-200"
                                }`}
                              >
                                {count}
                              </Badge>
                            </div>
                          </div>
                          <ScrollArea
                            className={`border rounded-b-lg h-[400px] p-2 shadow-sm transition-all duration-200 ${
                              isSelected
                                ? "border-[#294d48] bg-[#F6FAFA]"
                                : hasEnquiries
                                ? "border-[#D8DCDB] bg-white/80 backdrop-blur-sm"
                                : "border-gray-200 bg-gray-50/80"
                            }`}
                          >
                            <div
                              className="space-y-2"
                              key={`stage-${stageId}-${searchQuery}-${groupedEnquiries[stageId]?.length || 0}`}
                            >
                              {(() => {
                                // Log the entire array for this stage before mapping
                                console.log(`STAGE ${stageId} - Total items to render:`, groupedEnquiries[stageId]?.length);

                                // Create a map to check for duplicates
                                const uniqueIds = new Map();
                                groupedEnquiries[stageId]?.forEach(e => {
                                  const compositeKey = `${e.id}-${e.current_status}`;
                                  if (uniqueIds.has(compositeKey)) {
                                    console.warn(`DUPLICATE FOUND in ${stageId}:`, compositeKey);
                                  } else {
                                    uniqueIds.set(compositeKey, true);
                                  }
                                });
                                
                                  //Write the code here

                                  const currentStage = KANBAN_STAGES.find(s => s.id === stageId);
  
                                  // If the stage has subsections, render StepList
                                  if (currentStage && currentStage?.subsections && (groupedEnquiries[stageId] &&
                                    groupedEnquiries[stageId]?.length !== 0)) {
                                    // Transform subsections to steps format expected by StepList
                                    const steps = currentStage?.subsections.map(subsection => ({
                                      id: subsection?.id,
                                      label: subsection?.name
                                    }));
                                    
                                    // Create data list for StepList by mapping enquiries to their respective subsections
                                    const dataList = groupedEnquiries[stageId]?.flatMap(enquiry => {
                                      // Find which subsection this enquiry belongs to
                                      const subsection = currentStage.subsections.find(sub => 
                                        sub.states.includes(enquiry.current_status)
                                      );
                                      
                                      if (subsection) {
                                        return [{
                                          id: `${enquiry.id}-${subsection.id}`,
                                          step: subsection.id,
                                          content: (
                                            <EnquiryMiniCard
                                              key={`${enquiry.id}-${enquiry.current_status}-${enquiry.enquiry_id || ''}`}
                                              enquiry={enquiry}
                                              onClick={() => handleEnquiryClick(enquiry)}
                                            />
                                          )
                                        }];
                                      }
                                      return [];
                                    }) || [];
                                    
                                    return (
                                      <StepList 
                                        steps={steps} 
                                        dataList={dataList} 
                                      />
                                    );
                                  }

                                return groupedEnquiries[stageId]?.map(
                                  (enquiry: EnquiryType) => {
                                    return (
                                      <EnquiryMiniCard
                                        key={`${enquiry.id}-${enquiry.current_status}-${enquiry.enquiry_id || ''}`}
                                        enquiry={enquiry}
                                        onClick={() => handleEnquiryClick(enquiry)}
                                      />
                                    );
                                  }
                                );
                              })()}
                              {(!groupedEnquiries[stageId] ||
                                groupedEnquiries[stageId].length === 0) && (
                                <div className="flex justify-center items-center h-16 text-gray-400 text-sm italic">
                                  No enquiries
                                </div>
                              )}
                            </div>
                          </ScrollArea>
                        </div>
                      );
                    })}
                  </div>
                </div>
              ))}
            </div>
          </>
        )}
      </Card>

      {/* Lifecycle Dialog */}
      <EnquiryLifecycleDialog
        selectedEnquiry={selectedEnquiry}
        isOpen={!!selectedEnquiry}
        onClose={handleCloseDialog}
        refetch={refetch}
      />
    </div>
  );
};

export default EnquiryKanban;

const StepList = ({ steps, dataList }) => {
  const [openStep, setOpenStep] =  useState(steps.length > 0 ? steps[0].id : null);

  // Data grouped by step
  const groupedData = steps.reduce((acc, step) => {
    acc[step.id] = dataList.filter(item => item.step === step.id);
    return acc;
  }, {});

  return (
    <div style={{ maxWidth: 400, margin: "0 auto" }}>
      {steps.map((step, idx) => {
          const itemCount = groupedData[step.id]?.length || 0;
          const hasItems = itemCount > 0;
          const isOpen = openStep === step.id;
       return (
         <React.Fragment key={step.id}>
           <div
             style={{
               cursor: "pointer",
               background: "#f0f0f0",
               padding: 8,
               borderRadius: 6,
               fontWeight: openStep === step.id ? "normal" : "normal",
               boxShadow: openStep === step.id ? "0 2px 8px #ccc" : "none",
               marginBottom: 8,
             }}
             onClick={() => setOpenStep(openStep === step.id ? null : step.id)}
           >
             <div className="flex items-center justify-between">
             <span className="mr-2 font-medium" style={{ fontSize: "0.95rem" }}>
                {step.label}
            </span>
               <div className="flex items-center gap-2">
                <Badge 
                  variant={hasItems ? "default" : "outline"}
                  className={`${
                    hasItems
                      ? "bg-[#294d48] hover:bg-[#294d48]/80"
                      : "bg-transparent text-gray-400 border-gray-200"
                  }`}
                >
                  {itemCount}
                </Badge>
                {isOpen ? 
                  <ChevronUp className="h-6 w-4 text-[#294d48]" /> : 
                  <ChevronDown className="h-6 w-4 text-[#294d48]" />
                }
              </div>
             </div>
           </div>
           {openStep === step.id && (
             <>
               <div
                 style={{
                   background: "#fff",
                   maxHeight: 290,
                   minHeight: 290,
                   overflowY: "auto",
                 }}
               >
                 {groupedData[step.id].length > 0 ? (
                   groupedData[step.id].map((item) => (
                     <div key={item.id} style={{ marginBottom: 8 }}>
                       {item.content}
                     </div>
                   ))
                 ) : (
                  <div className="flex justify-center items-center h-full w-full py-8">
                  <div style={{ color: "#aaa" }}>No data for this section.</div>
                </div>
                 )}
               </div>
             </>
           )}
         </React.Fragment>
       );
})}
    </div>
  );
};
