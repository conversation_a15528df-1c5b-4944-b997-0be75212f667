import { useState, useEffect } from 'react';
import { OpenProjects } from './OpenProjects';
import { RecentActivity } from './RecentActivity';
import { SalesProgress } from './SalesProgress';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { useLocation } from 'react-router-dom';
import { useProjectsData } from '@/hooks/use-projects-data';
import { supabase } from '@/integrations/supabase/client';
import { formatEmailToName } from '@/utils/format';

interface Customer {
  id: string;
  customer_id: string;
  name: string;
  account_owner?: string;
}

interface AccountOwner {
  id: string;
  name: string;
}

interface CurrentSnapshotProps {
  selectedCustomerId?: number | string | null;
}

export const CurrentSnapshot = ({ selectedCustomerId }: CurrentSnapshotProps) => {
  const [selectedCustomer, setSelectedCustomer] = useState('All');
  const [selectedAccountOwner, setSelectedAccountOwner] = useState(null);
  const [customers, setCustomers] = useState<Customer[]>([]);
  const [isAdmin, setIsAdmin] = useState(false);
  const [currentUser, setCurrentUser] = useState<any>(null);
  const location = useLocation();

  // Fetch user role and set account owner
  useEffect(() => {
    async function fetchUserRole() {
      try {
        const user = {
          email: localStorage.getItem('userEmail'),
          id: localStorage.getItem('userId'),
          role: localStorage.getItem('userRole'),
          country: localStorage.getItem('userCountry'),
          category: localStorage.getItem('userCategory')
        }
        if (user?.email) {
          setCurrentUser(user);
            
          setIsAdmin(user?.role === 'admin' || user?.role === 'bu_head');
          
          // If sales, set selected owner to current user
          if (user?.role === 'sales') {
            setSelectedAccountOwner(user.email);
          } else {
            setSelectedAccountOwner("All")
          }
        }
      } catch (error) {
        console.error("Error fetching user role:", error);
      }
    }
    fetchUserRole();
  }, []);

  // Fetch customers based on user role
  useEffect(() => {
    async function fetchCustomers() {
      try {
        let query = supabase
          .from('customer')
          .select('id, customer_id, customer_full_name, account_owner')
          .order('customer_full_name');

        // If sales, only fetch customers for the current user
        if (currentUser?.role === 'sales') {
          query = query.eq('account_owner', currentUser.email);
        } else if (currentUser?.role === 'bu_head') {
          // First get all users of the category from user_roles
          const { data: categoryUsers, error: usersError } = await supabase
            .from('user_list')
            .select('email, user_roles!inner(*)')
            .eq('user_roles.category', currentUser?.category);

          if (usersError) throw usersError;

          // Get the list of emails from the users
          const accountOwners = categoryUsers?.map(user => user.email) || [];

          // Filter customers by these account owners
          query = query.in('account_owner', accountOwners);
        }

        const { data, error } = await query;

        if (error) throw error;

        const formattedCustomers = [
          { id: 'All', customer_id: 'All', name: 'All Customers'},
          ...data.map(customer => ({
            id: customer.id,
            customer_id: customer.customer_id,
            name: customer.customer_full_name,
            account_owner: customer.account_owner
          }))
        ];

        setCustomers(formattedCustomers);
      } catch (error) {
        console.error('Error fetching customers:', error);
      }
    }

    if (currentUser) {
      fetchCustomers();
    }
  }, [currentUser]);

  // Set selected customer when selectedCustomerId changes
  useEffect(() => {
    if (selectedCustomerId) {
      const customer = customers.find(c => c.id === selectedCustomerId.toString());
      if (customer) {
        setSelectedCustomer(customer.id);
        if (customer.account_owner) {
          setSelectedAccountOwner(customer.account_owner);
        }
      }
    }
  }, [selectedCustomerId, customers]);

  // Get unique account owners from customers
  const getUniqueAccountOwners = (): AccountOwner[] => {
    const allOwners = new Set<string>();
    
    // Add owners from customers
    customers.forEach(customer => {
      if (customer.account_owner) {
        allOwners.add(customer.account_owner);
      }
    });

    // Convert Set to array and sort alphabetically
    return [
      { id: 'All', name: 'All Account Owners' },
      ...Array.from(allOwners)
        .sort()
        .map(owner => ({ id: owner, name: formatEmailToName(owner) }))
    ];
  };

  const accountOwners = getUniqueAccountOwners();
  
  // Apply customer filter from navigation state
  useEffect(() => {
    if (location.state?.selectedCustomerName) {
      // Find customer by name or use default
      const customer = customers.find(c => c.name === location.state.selectedCustomerName);
      
      if (customer) {
        setSelectedCustomer(customer.id);
      }
    }
  }, [location.state, customers]);
  
  return (
    <div className="flex flex-col h-full">
      <div className="flex justify-between items-center mb-4">
        <h2 className="text-2xl font-semibold">Current Snapshot</h2>
        <div className="flex gap-2">
          <div className="w-56">
            <Select 
              value={selectedAccountOwner} 
              onValueChange={setSelectedAccountOwner}
              disabled={!isAdmin}
            >
              <SelectTrigger>
                <SelectValue placeholder="Filter by account owner" />
              </SelectTrigger>
              <SelectContent>
                {accountOwners.map(owner => (
                  <SelectItem key={owner.id} value={owner.id}>
                    {owner.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
          <div className="w-56">
            <Select 
              value={selectedCustomer} 
              onValueChange={setSelectedCustomer}
              disabled={selectedAccountOwner !== 'All' && !customers.some(c => c.account_owner === selectedAccountOwner)}
            >
              <SelectTrigger>
                <SelectValue placeholder="Filter by customer" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="All">All Customers</SelectItem>
                {customers
                  .filter(customer => 
                    (selectedAccountOwner === 'All' || customer.account_owner === selectedAccountOwner) &&
                    customer.id !== 'All'  // Exclude the "All Customers" option from the filtered list
                  )
                  .map(customer => (
                    <SelectItem key={customer.id} value={customer.id}>
                      {customer.name}
                    </SelectItem>
                  ))
                }
              </SelectContent>
            </Select>
          </div>
        </div>
      </div>
      
      {/* Pass both filters to the components */}
      <OpenProjects selectedCustomer={selectedCustomer} selectedAccountOwner={selectedAccountOwner}  />
      <SalesProgress selectedCustomer={selectedCustomer} selectedAccountOwner={selectedAccountOwner} customerId={customers.find(c => c.id === selectedCustomer)?.customer_id}/>
      <RecentActivity selectedCustomer={selectedCustomer} selectedAccountOwner={selectedAccountOwner} />
    </div>
  );
};
