import React, { useEffect, useState, useRef } from "react";
import {
  ArrowLeft,
  FileText,
  Package,
  ShoppingBag,
  ChevronDown,
  ChevronUp,
  Calendar,
  BarChart,
  Briefcase,
  Edit,
  Plus,
  MapPin,
  Users,
  Mail,
  Phone,
  Layers,
  X,
  ChevronRight,
  ChevronLeft,
  Eye,
  PenSquare,
  Trash2,
  Loader2,
  Check,
  History,
  Clock,
} from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion";
import { Customer } from "./CustomerList";
import { useNavigate } from "react-router-dom";
import { toast } from "sonner";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from "@/components/ui/collapsible";
import { Input } from "@/components/ui/input";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
  DialogFooter,
} from "@/components/ui/dialog";
import { useCustomerDetails } from "@/hooks/use-customer-details";
import { useAccountPlanning } from "@/hooks/use-account-planning";
import { supabase } from "@/integrations/supabase/client";
import { AddCustomerForm } from "./AddCustomerForm";
import { useCustomerMetrics } from "@/hooks/use-customer-metrics";
import { useDebounce } from "@/hooks/useDebounce";
import axios from "axios";
import { API_CONFIG } from "@/config/api";
import { Badge } from "@/components/ui/badge";
import { useCustomerActivities } from "@/hooks/use-customer-activities";
import { Textarea } from "@/components/ui/textarea";

// Add this type definition at the top of the file, after the imports
type Compound = {
  id: string;
  compound_id: string;
  chemical_name: string;
  // Add other fields if needed
};

// Add these type definitions after the existing types
type SortField =
  | "potentialOrderValue"
  | "expectedQuarterlyProcurement"
  | "requestDate"
  | "closureDate"
  | "criticality"
  | "eta"
  | "orderValue"
  | "orderDate"
  | "deliveryDate";
type SortDirection = "asc" | "desc";

const statusOptions = [
  "Cold - No Response",
  "Warm - Enquiry Stage_Supply",
  "Warm - Enquiry Stage_Customer",
  "Warm - Pitched",
  "Enquiry Lost",
  "Hot - Sampling Stage_Supply",
  "Hot - Sampling Stage_Customer",
  "Hot - Sample Approved",
  "Order Received",
  "Order Fulfilled, pending repeat order",
  "Active recurring orders",
  "Cold - No Response After Enquiry",
  "Discontinued",
];

interface ChemicalInPO {
  chemical_name: string;
  margin: string;
  deliveryDate: string;
  paymentTerms: string;
  expectedQuarterlyProcurement: string;
  salesOrderValue: string;
  current_status?: string;
  eta?: string;
}

interface GroupedPO {
  poNumber: string;
  orderValue: string;
  orderDate: string;
  repeatFrequency: string;
  chemicals: ChemicalInPO[];
}

// Remove the static pipelineData array and add this function
const calculatePipelineData = (chemicals: any[]) => {
  return statusOptions.map((status, index) => {
    // Filter chemicals by status
    const chemicalsInStatus = chemicals.filter(
      (chem) => chem.status === status
    );

    // Calculate total achievement value for this status
    const targetSum = chemicalsInStatus.reduce((sum, chem) => {
      return sum + (chem.achievement_value_current || 0);
    }, 0);

    return {
      id: index + 1,
      status: `${index + 1}. ${status}`,
      products: chemicalsInStatus.length.toString(),
      targetSum: targetSum.toLocaleString(),
    };
  });
};

// Add this mapping function near the top of the file, after imports
const getUnitDisplayName = (unit: string | null) => {
  if (!unit) return "";
  const unitMap: { [key: string]: string } = {
    "Metric Ton (mt)": "Mt",
    "Pound (lb)": "Pound",
    "Gallon (gal)": "Gallon",
    "Litre (L)": "Litre",
    "Kilolitre (Kl)": "Kl",
    "Kilogram (Kg)": "Kg",
  };
  return unitMap[unit] || unit;
};

// Add ActivityTypeIcon component
const ActivityTypeIcon = ({ type }: { type: string }) => {
  switch (type) {
    case "In-person Meeting":
      return <Users className="h-4 w-4" />;
    case "Virtual Meeting":
      return <Clock className="h-4 w-4" />;
    case "Call":
      return <Phone className="h-4 w-4" />;
    case "Sample Request":
      return <Calendar className="h-4 w-4" />;
    case "Enquiry":
      return <Calendar className="h-4 w-4" />;
    case "Follow-up":
      return <Clock className="h-4 w-4" />;
    default:
      return <Calendar className="h-4 w-4" />;
  }
};

// Add activity type mapping
const activityTypeMap: Record<string, string> = {
  enquiry_created: "Enquiry Created",
  enquiry_updated: "Enquiry Updated",
  enquiry_closed: "Enquiry Closed",
  sample_created: "Sample Created",
  sample_updated: "Sample Updated",
  sample_closed: "Sample Closed",
  po_created: "Purchase Order Created",
  po_updated: "Purchase Order Updated",
  po_closed: "Purchase Order Closed",
  meeting_created: "Meeting Created",
  meeting_updated: "Meeting Updated",
  meeting_cancelled: "Meeting Cancelled",
  meeting_completed: "Meeting Completed",
  remark_added: "Remark Added",
  remark_updated: "Remark Updated",
  status_changed: "Status Changed",
  target_updated: "Target Updated",
  customer_created: "Customer Created",
  customer_updated: "Customer Updated",
};

// Add formatActivityType function
const formatActivityType = (type: string | undefined | null) => {
  if (!type) return "Unknown Activity";
  return (
    activityTypeMap[type] ||
    type
      .split("_")
      .map((word) => word.charAt(0).toUpperCase() + word.slice(1))
      .join(" ")
  );
};

export const CustomerDetail = ({
  customerId,
  commonCustomerId,
  onBack,
}: {
  customerId: number;
  commonCustomerId: string;
  onBack: () => void;
}) => {
  const { customer, enquiries, samples, purchaseOrders, loading, error } =
    useCustomerDetails(customerId, commonCustomerId);
  const [editBasicInfo, setEditBasicInfo] = useState(false);
  const [editContactInfo, setEditContactInfo] = useState(false);
  const navigate = useNavigate();
  const [chemicals, setChemicals] = useState(customer?.planning || []);
  const [editingId, setEditingId] = useState<string | null>(null);
  const [enquiriesFilter, setEnquiriesFilter] = useState("all");
  const [samplesFilter, setSamplesFilter] = useState("all");
  const [posFilter, setPosFilter] = useState("all");
  const [metricsPeriod, setMetricsPeriod] = useState("currentQuarter");
  const [accountPlanningExpanded, setAccountPlanningExpanded] = useState(false);
  const [customerData, setCustomerData] = useState(customer || {});
  const [activeTab, setActiveTab] = useState("enquiries");
  const [enquiriesPage, setEnquiriesPage] = useState(1);
  const [samplesPage, setSamplesPage] = useState(1);
  const [posPage, setPosPage] = useState(1);
  const itemsPerPage = 10;
  const [isAddChemicalDialogOpen, setIsAddChemicalDialogOpen] = useState(false);
  const [newChemicalName, setNewChemicalName] = useState("");
  const [selectedChemicalId, setSelectedChemicalId] = useState<string | null>(
    null
  );
  const [showSuggestions, setShowSuggestions] = useState(false);
  const suggestionsRef = useRef<HTMLDivElement>(null);
  const inputRef = useRef<HTMLInputElement>(null);
  const [customerSubmitLoading, setCustomerSubmitLoading] = useState(false);
  const [searchTerm, setSearchTerm] = useState("");
  const debouncedSearchTerm = useDebounce(searchTerm, 300);
  const [chemicalNameSuggestions, setChemicalNameSuggestions] = useState<
    string[]
  >([]);
  const [chemicalNameToCompoundMap, setChemicalNameToCompoundMap] = useState<
    Map<string, Compound>
  >(new Map());
  const userRole = localStorage.getItem("userRole");
  const isAdmin = userRole === "admin";
  const [sortField, setSortField] = useState<SortField>("requestDate");
  const [sortDirection, setSortDirection] = useState<SortDirection>("desc");
  const [samplesSortField, setSamplesSortField] =
    useState<SortField>("requestDate");
  const [samplesSortDirection, setSamplesSortDirection] =
    useState<SortDirection>("desc");
  const [posSortField, setPosSortField] = useState<SortField>("orderDate");
  const [posSortDirection, setPosSortDirection] =
    useState<SortDirection>("desc");
  const [recentActivityExpanded, setRecentActivityExpanded] = useState(false);
  const [timeFilter, setTimeFilter] = useState('last7days');
  const timeFilterOptions = [
    { value: 'last7days', label: 'Last 7 Days' },
    { value: 'currentWeek', label: 'Current Week' },
    { value: 'lastWeek', label: 'Last Week' },
    { value: 'currentMonth', label: 'Current Month' },
    { value: 'lastMonth', label: 'Last Month' },
    { value: 'all', label: 'All Time' },
  ];
  const recentActivityRef = useRef<HTMLDivElement>(null);
  const accountPlanningRef = useRef<HTMLDivElement>(null);

  const {
    updateAccountPlanningItem,
    deleteAccountPlanningItem,
    addAccountPlanningItem,
    isSubmitting,
  } = useAccountPlanning();

  const [activities, setActivities] = useState<any[]>([]);
  const [activitiesLoading, setActivitiesLoading] = useState(false);
  const [activitiesError, setActivitiesError] = useState<string | null>(null);
  const [editingRemarks, setEditingRemarks] = useState<string | null>(null);
  const [remarksEditForm, setRemarksEditForm] = useState<{
    current: string;
    previous: string;
  }>({
    current: "",
    previous: "",
  });

  const [poFilter, setPoFilter] = useState<'all' | 'open' | 'delivered'>('all');

  // Fetch activities when customerData changes
  useEffect(() => {
    const fetchActivities = async () => {
      if (!customerData?.id) return;

      setActivitiesLoading(true);
      setActivitiesError(null);

      try {
        // Refresh activities after update
        const { data, error } = await supabase
        .from('customer')
        .select(`
          id,
          customer_full_name,
          activities:customer_activity!inner(
            id,
            activity_type,
            description,
            created_at,
            enquiries:enquiries(*)
          ),
          remarks_history:customer_activity_history(
            remarks,
            created_at
          )
        `)
        .eq('id', customerData.id)

        if (error) throw error;

        if (data && data[0]) {
          // Sort activities by created_at in descending order
          const sortedActivities = data[0].activities.sort((a, b) => 
            new Date(b.created_at).getTime() - new Date(a.created_at).getTime()
          );
          
          // Get the latest and previous remarks from the customer's remarks history
          const latestRemarks = data[0].remarks_history[data[0].remarks_history.length - 1]?.remarks || "";
          const previousRemarks = data[0].remarks_history[data[0].remarks_history.length - 2]?.remarks || "";
          
          setActivities(
            sortedActivities.map((activity) => ({
              ...activity,
              enquiryId: activity.enquiries?.id || '',
              type: activity.activity_type || '',
              customerId: customer.id,
              customerName: customer.customer_full_name || '',
              reviewRemarksCurrent: latestRemarks,
              reviewRemarksPrevious: previousRemarks,
              remarks_history: data[0].remarks_history || []
            }))
          );
        }
      } catch (error) {
        console.error("Error fetching activities:", error);
        setActivitiesError("Failed to load activities");
      } finally {
        setActivitiesLoading(false);
      }
    };

    fetchActivities();
  }, [customerData?.id]);

  const { metrics, loading: metricsLoading } = useCustomerMetrics(
    commonCustomerId || "",
    metricsPeriod === "ytd"
      ? "ytd"
      : metricsPeriod === "previousQuarter"
      ? "previous_quarter"
      : "current_quarter",
    customerId?.toString()
  );

  useEffect(() => {
    setCustomerData(customer);
    setChemicals(customer?.planning || []);
  }, [customer]);

  useEffect(() => {
    searchChemicalSuggestions(debouncedSearchTerm);
  }, [debouncedSearchTerm]);

  const searchChemicalSuggestions = async (searchTerm: string) => {
    if (!searchTerm || searchTerm.length < 2) {
      setChemicalNameSuggestions([]);
      return;
    }

    try {
      const response = await axios.get(
        `${API_CONFIG.catalogBaseUrl}catalog/search`,
        {
          params: { q: searchTerm },
          headers: {
            accept: "application/json",
            Authorization: `Bearer ${API_CONFIG.accessToken}`,
          },
        }
      );

      const compounds = response.data.data || [];
      const catalogResultsWithSynonyms = compounds.map((compound) => ({
        displayName: compound.synonym
          ? `${compound.synonym} (${compound.chemical_name})`
          : compound.chemical_name,
        chemicalName: compound.chemical_name,
        compoundId: compound.compound_id,
        synonym: compound.synonym,
      }));

      // Store the mapping of chemical names to their full compound data with proper typing
      const newMap = new Map<string, Compound>();
      compounds.forEach((compound) => {
        // Map for chemical name
        newMap.set(compound.chemical_name, compound);
        // Map for synonym if it exists
        if (compound.synonym) {
          newMap.set(compound.synonym, compound);
        }
      });
      setChemicalNameToCompoundMap(newMap);

      // Set suggestions with synonyms
      setChemicalNameSuggestions(
        catalogResultsWithSynonyms.map((item) => item.displayName)
      );
    } catch (error) {
      console.error("Error searching chemical suggestions:", error);
      setChemicalNameSuggestions([]);
      setChemicalNameToCompoundMap(new Map());
    }
  };

  // Show loading state
  if (loading && customerSubmitLoading) {
    return (
      <div className="flex flex-col h-full items-center justify-center p-8">
        <Loader2 className="h-8 w-8 text-primary animate-spin mb-4" />
        <p className="text-muted-foreground">Loading customer details...</p>
      </div>
    );
  }

  // Show error state if there's an error
  if (error) {
    return (
      <div className="flex flex-col h-full items-center justify-center p-8">
        <div className="text-destructive mb-2">
          Error loading customer details
        </div>
        <Button variant="outline" onClick={onBack}>
          <ArrowLeft size={16} className="mr-2" />
          Go Back
        </Button>
      </div>
    );
  }

  // Enquiries filter and search
  let filteredEnquiries = enquiries.filter((item) => {
    if (enquiriesFilter === "all") return true;
    if (enquiriesFilter === "open") return !item.quotationFeedback;
    if (enquiriesFilter === "closed") return !!item.quotationFeedback;
    return true;
  });
  if (searchTerm.trim() && activeTab === 'enquiries') {
    const lower = searchTerm.toLowerCase();
    filteredEnquiries = filteredEnquiries.filter(item =>
      (item.chemical_name && item.chemical_name.toLowerCase().includes(lower)) ||
      (item.enquiry_id && String(item.enquiry_id).toLowerCase().includes(lower)) ||
      (item.current_status && item.current_status.toLowerCase().includes(lower))
    );
  }

  // Samples filter and search
  let filteredSamples = samples.filter((item) => {
    if (samplesFilter === "all") return true;
    if (samplesFilter === "open") return !item.sampleFeedback;
    if (samplesFilter === "closed") return !!item.sampleFeedback;
    return true;
  });
  if (searchTerm.trim() && activeTab === 'samples') {
    const lower = searchTerm.toLowerCase();
    filteredSamples = filteredSamples.filter(item =>
      (item.chemical_name && item.chemical_name.toLowerCase().includes(lower)) ||
      (item.enquiryId && String(item.enquiryId).toLowerCase().includes(lower)) ||
      (item.current_status && item.current_status.toLowerCase().includes(lower))
    );
  }

  // PO filter and search
  let filteredPOs = purchaseOrders.filter((item) => {
    const isDelivered = item.deliveryDate && item.deliveryDate !== 'N/A';
    if (poFilter === 'all') return true;
    if (poFilter === 'open') return !isDelivered;
    if (poFilter === 'delivered') return isDelivered;
    return true;
  });
  if (searchTerm.trim() && activeTab === 'pos') {
    const lower = searchTerm.toLowerCase();
    filteredPOs = filteredPOs.filter(item =>
      (item.po_number && String(item.po_number).toLowerCase().includes(lower)) ||
      (item.chemical_name && item.chemical_name.toLowerCase().includes(lower)) ||
      (item.orderValue && String(item.orderValue).toLowerCase().includes(lower)) ||
      (item.deliveryDate && String(item.deliveryDate).toLowerCase().includes(lower))
    );
  }

  // Group POs by PO number
  const groupedPOs = filteredPOs.reduce<Record<string, GroupedPO>>(
    (acc, po) => {
      if (!acc[po.po_number]) {
        acc[po.po_number] = {
          poNumber: po.po_number,
          orderValue: po.orderValue,
          orderDate: po.orderDate,
          repeatFrequency: po.repeatFrequency,
          chemicals: [],
        };
      }
      acc[po.po_number].chemicals.push({
        chemical_name: po.chemical_name,
        margin: po.margin || "N/A",
        deliveryDate: po.deliveryDate || "N/A",
        paymentTerms: po.paymentTerms || "N/A",
        expectedQuarterlyProcurement: po.expectedQuarterlyProcurement || "N/A",
        salesOrderValue: po.salesOrderValue || "N/A",
        current_status: po.current_status,
        eta: po.eta,
      });
      return acc;
    },
    {}
  );

  const getSortedEnquiries = (enquiries: any[]) => {
    return [...enquiries].sort((a, b) => {
      let valueA, valueB;

      switch (sortField) {
        case "potentialOrderValue":
          valueA = parseFloat(
            a.potentialOrderValue?.replace(/[^0-9.-]+/g, "") || "0"
          );
          valueB = parseFloat(
            b.potentialOrderValue?.replace(/[^0-9.-]+/g, "") || "0"
          );
          break;
        case "expectedQuarterlyProcurement":
          valueA = parseFloat(
            a.expectedQuarterlyProcurement?.replace(/[^0-9.-]+/g, "") || "0"
          );
          valueB = parseFloat(
            b.expectedQuarterlyProcurement?.replace(/[^0-9.-]+/g, "") || "0"
          );
          break;
        case "requestDate":
          valueA = new Date(a.requestDate || "").getTime();
          valueB = new Date(b.requestDate || "").getTime();
          break;
        case "closureDate":
          valueA = new Date(a.closureDate || "").getTime();
          valueB = new Date(b.closureDate || "").getTime();
          break;
        case "criticality":
          const criticalityOrder = { high: 3, medium: 2, low: 1 };
          valueA = criticalityOrder[a.confidence?.toLowerCase() || "low"] || 0;
          valueB = criticalityOrder[b.confidence?.toLowerCase() || "low"] || 0;
          break;
        default:
          return 0;
      }

      return sortDirection === "asc" ? valueA - valueB : valueB - valueA;
    });
  };

  const paginatedEnquiries = getSortedEnquiries(filteredEnquiries).slice(
    (enquiriesPage - 1) * itemsPerPage,
    enquiriesPage * itemsPerPage
  );

  const getSortedSamples = (samples: any[]) => {
    return [...samples].sort((a, b) => {
      let valueA, valueB;

      switch (samplesSortField) {
        case "potentialOrderValue":
          valueA = parseFloat(
            a.potentialOrderValue?.replace(/[^0-9.-]+/g, "") || "0"
          );
          valueB = parseFloat(
            b.potentialOrderValue?.replace(/[^0-9.-]+/g, "") || "0"
          );
          break;
        case "expectedQuarterlyProcurement":
          valueA = parseFloat(
            a.expectedQuarterlyProcurement?.replace(/[^0-9.-]+/g, "") || "0"
          );
          valueB = parseFloat(
            b.expectedQuarterlyProcurement?.replace(/[^0-9.-]+/g, "") || "0"
          );
          break;
        case "requestDate":
          valueA = new Date(a.requestDate || "").getTime();
          valueB = new Date(b.requestDate || "").getTime();
          break;
        case "closureDate":
          valueA = new Date(a.closureDate || "").getTime();
          valueB = new Date(b.closureDate || "").getTime();
          break;
        case "criticality":
          const criticalityOrder = { high: 3, medium: 2, low: 1 };
          valueA = criticalityOrder[a.confidence?.toLowerCase() || "low"] || 0;
          valueB = criticalityOrder[b.confidence?.toLowerCase() || "low"] || 0;
          break;
        case "eta":
          if (!a.eta || a.eta === "") {
            valueA =
              sortDirection === "asc"
                ? Number.MAX_SAFE_INTEGER
                : Number.MIN_SAFE_INTEGER;
          } else {
            valueA = new Date(a.eta).getTime();
          }
          if (!b.eta || b.eta === "") {
            valueB =
              sortDirection === "asc"
                ? Number.MAX_SAFE_INTEGER
                : Number.MIN_SAFE_INTEGER;
          } else {
            valueB = new Date(b.eta).getTime();
          }
          break;
        default:
          return 0;
      }

      return samplesSortDirection === "asc" ? valueA - valueB : valueB - valueA;
    });
  };

  const paginatedSamples = getSortedSamples(filteredSamples).slice(
    (samplesPage - 1) * itemsPerPage,
    samplesPage * itemsPerPage
  );

  const getSortedPOs = (pos: [string, GroupedPO][]) => {
    return [...pos].sort(([_, poA], [__, poB]) => {
      let valueA, valueB;

      switch (posSortField) {
        case "orderValue":
          valueA = parseFloat(poA.orderValue?.replace(/[^0-9.-]+/g, "") || "0");
          valueB = parseFloat(poB.orderValue?.replace(/[^0-9.-]+/g, "") || "0");
          break;
        case "orderDate":
          valueA = new Date(poA.orderDate || "").getTime();
          valueB = new Date(poB.orderDate || "").getTime();
          break;
        case "deliveryDate":
          valueA = new Date(poA.chemicals[0]?.deliveryDate || "").getTime();
          valueB = new Date(poB.chemicals[0]?.deliveryDate || "").getTime();
          break;
        case "eta":
          valueA = new Date(poA.chemicals[0]?.eta || "").getTime();
          valueB = new Date(poB.chemicals[0]?.eta || "").getTime();
          break;
        default:
          return 0;
      }

      return posSortDirection === "asc" ? valueA - valueB : valueB - valueA;
    });
  };

  const paginatedPOs = getSortedPOs(Object.entries(groupedPOs)).slice(
    (posPage - 1) * itemsPerPage,
    posPage * itemsPerPage
  );

  const getTotalPages = (tab: string) => {
    switch (tab) {
      case "enquiries":
        return Math.ceil(filteredEnquiries.length / itemsPerPage);
      case "samples":
        return Math.ceil(filteredSamples.length / itemsPerPage);
      case "pos":
        return Math.ceil(Object.keys(groupedPOs).length / itemsPerPage);
      default:
        return 1;
    }
  };

  const handleCustomerUpdate = async (
    updatedCustomer: Omit<Customer, "id">
  ) => {
    try {
      setCustomerSubmitLoading(true);

      // Prepare the customer data for Supabase based on form type
      const customerData = {
        ...(editBasicInfo && {
          customer_full_name: updatedCustomer.customer_full_name,
          customer_company: updatedCustomer.customer_full_name,
          city: updatedCustomer.city || "",
          country: updatedCustomer.country || "",
          industries: updatedCustomer.industries || [],
          annual_turnover: updatedCustomer.annualRevenue || "0-25",
          annual_chemicals_procurement: parseFloat(
            updatedCustomer.expectedQuarterlyProcurement?.replace(
              /[$,]/g,
              ""
            ) || "0"
          ),
        }),
        modified_at: new Date().toISOString(),
      };

      // Update the customer in Supabase
      const { data, error } = await supabase
        .from("customer")
        .update(customerData)
        .eq("id", customerId.toString())
        .select();

      if (error) {
        throw error;
      }

      // Only update contacts if it's the contact form and contacts have changed
      if (
        editContactInfo &&
        updatedCustomer.contacts &&
        updatedCustomer.contacts.length > 0
      ) {
        const { error: contactsError } = await supabase
          .from("customer_contact")
          .upsert(
            updatedCustomer.contacts.map((contact) => ({
              id: contact.id, // Include the id for existing contacts
              customer_id: customerId.toString(),
              name: contact.name,
              role: contact.role,
              email: contact.email,
              phone: contact.phone,
              is_primary: contact.role === "sales" || false,
            })),
            {
              onConflict: "id", // Specify conflict resolution on id
              ignoreDuplicates: false, // Update existing records
            }
          );

        if (contactsError) {
        }
      }

      // Update local state
      setCustomerData({
        ...customerData,
        ...updatedCustomer,
      });
      setEditBasicInfo(false);
      setEditContactInfo(false);
      setCustomerSubmitLoading(false);
      toast.success("Customer updated successfully");
    } catch (error) {
      setCustomerSubmitLoading(false);
      console.error("Error updating customer:", error);
      toast.error("Failed to update customer: " + (error as Error).message);
    }
  };

  const handleViewMeetings = () => {
    navigate("/crm", {
      state: {
        activeTab: "meetings",
        selectedCustomer: customerData.id,
        selectedCustomerName: customerData.name,
      },
    });
  };

  const handleViewSnapshot = () => {
    navigate("/crm", {
      state: {
        activeTab: "snapshot",
        selectedCustomer: customerData.id,
        selectedCustomerName: customerData.name,
      },
    });
  };

  const handleAddChemical = async () => {
    if (!customerData?.id) {
      toast.error("Customer ID is required to add a chemical");
      return;
    }

    setIsAddChemicalDialogOpen(true);
  };

  const handleSubmitNewChemical = async () => {
    if (!newChemicalName.trim()) {
      toast.error("Chemical name is required");
      return;
    }

    // Extract the actual chemical name if it's a synonym
    // Handle cases like "RS 120 (nitrate) (IPA)" by taking the last parenthetical group
    const match = newChemicalName.match(/^(.*?)\s*\(([^()]+)\)$/);
    const actualChemicalName = match ? match[2] : newChemicalName;

    // Find the selected compound (optional - for catalog mapping)
    const selectedCompound = chemicalNameToCompoundMap.get(actualChemicalName);

    // Create a new chemical with the provided name
    const newChemical = {
      customer_id: customerData.id,
      chemical_name: newChemicalName.trim(), // Keep what user selected (including synonym if present)
      offset_chemical_id: selectedCompound?.compound_id || null,
      offset_chemical_name: selectedCompound?.chemical_name || null,
      grade: "",
      quarterly_volume: 0,
      quarterly_value: 0,
      previous_quarter_value: 0,
      target_this_quarter: 0,
      status: "Cold - No Response",
      conversion_probability: 0,
      comments: "",
    };

    try {
      const result = await addAccountPlanningItem(newChemical);

      if (result.success && result.data) {
        // Add the new chemical to the array
        setChemicals((prev) => [...prev, result.data]);

        // Enter edit mode for the new chemical
        setEditingId(result.data.id);

        // Reset and close dialog
        setNewChemicalName("");
        setSelectedChemicalId(null);
        setIsAddChemicalDialogOpen(false);
      }
    } catch (error) {
      console.error("Error adding chemical:", error);
      toast.error("Failed to add chemical");
    }
  };

  const toggleEdit = (id: string) => {
    if (editingId === id) {
      // If we're already editing this row, save the changes
      const chemical = chemicals.find((c) => c.id === id);
      if (chemical) {
        saveChemical(chemical);
      }
      setEditingId(null);
    } else {
      // If we're not editing this row, start editing
      setEditingId(id);
    }
  };

  const handleDeleteChemical = async (id: string) => {
    try {
      // Find the item to delete
      const itemToDelete = chemicals.find((chem) => chem.id === id);
      if (!itemToDelete) {
        toast.error("Could not find item to delete");
        return;
      }

      // Delete from database
      const result = await deleteAccountPlanningItem(itemToDelete);

      if (result.success) {
        // Remove the chemical from the array
        setChemicals((prev) => prev.filter((chem) => chem.id !== id));
      }
    } catch (error) {
      console.error("Error deleting chemical:", error);
      toast.error("Failed to remove chemical");
    }
  };

  // Handle field changes during edit
  const handleFieldChange = (id: string, field: string, value: any) => {
    setChemicals((prevChemicals) =>
      prevChemicals.map((chemical) =>
        chemical.id === id ? { ...chemical, [field]: value } : chemical
      )
    );
  };

  // Add a function to save the chemical
  const saveChemical = async (chemical: any) => {
    try {
      // Get the original chemical from the database for comparison
      const { data: originalData, error: fetchError } = await supabase
        .from("account_planning")
        .select("*")
        .eq("id", chemical.id)
        .single();

      if (fetchError) throw fetchError;

      // Update the chemical in the database
      const result = await updateAccountPlanningItem(chemical, originalData);

      if (result.success) {
        toast.success("Chemical updated successfully");
      } else {
        throw new Error(result.error?.message || "Failed to update chemical");
      }
    } catch (error) {
      console.error("Error saving chemical:", error);
      toast.error("Failed to save chemical");

      // Revert to the original data
      refreshChemicals();
    }
  };

  // Add a function to refresh the chemicals list
  const refreshChemicals = async () => {
    if (!customerData?.id) return;

    try {
      const { data, error } = await supabase
        .from("account_planning")
        .select("*")
        .eq("customer_id", customerData.id);

      if (error) throw error;

      setChemicals(data);
    } catch (error) {
      console.error("Error refreshing chemicals:", error);
      toast.error("Failed to refresh chemicals");
    }
  };

  // Handle chemical name typing
  const handleChemicalNameChange = (value: string) => {
    setNewChemicalName(value);
    setSearchTerm(value);
    setShowSuggestions(true);
  };

  // Handle chemical name selection from suggestions
  const handleChemicalSelect = (value: string) => {
    // Extract the actual chemical name if it's a synonym (format: "synonym (chemical_name)")
    const match = value.match(/^(.*?)\s*\((.*?)\)$/);
    const actualChemicalName = match ? match[2] : value;

    setNewChemicalName(value);
    setSearchTerm(value);
    const selectedCompound = chemicalNameToCompoundMap.get(actualChemicalName);
    if (selectedCompound) {
      setSelectedChemicalId(selectedCompound.id);
    }
    setShowSuggestions(false);
  };

  // Add effect to update offset details when map is updated
  useEffect(() => {
    if (newChemicalName) {
      const selectedCompound = chemicalNameToCompoundMap.get(newChemicalName);
      if (selectedCompound) {
        setSelectedChemicalId(selectedCompound.id);
      }
    }
  }, [chemicalNameToCompoundMap, newChemicalName]);

  // Filter suggestions with fuzzy search
  const filteredSuggestions = chemicalNameSuggestions.filter((name) => {
    const searchTerm = newChemicalName.toLowerCase();
    const optionValue = name.toLowerCase();

    // Exact match
    if (optionValue.includes(searchTerm)) return true;

    // Fuzzy match - check if all characters in search term appear in order
    const searchChars = searchTerm.split("");
    let lastIndex = -1;
    return searchChars.every((char) => {
      const index = optionValue.indexOf(char, lastIndex + 1);
      if (index === -1) return false;
      lastIndex = index;
      return true;
    });
  });

  // Add this after the chemicals state
  const pipelineData = calculatePipelineData(chemicals);

  // Add this function after the existing functions
  const handleSort = (field: SortField) => {
    if (sortField === field) {
      setSortDirection(sortDirection === "asc" ? "desc" : "asc");
    } else {
      setSortField(field);
      setSortDirection("asc");
    }
  };

  const getSortIcon = (field: SortField) => {
    if (sortField !== field)
      return <ChevronDown size={16} className="opacity-30" />;
    return sortDirection === "asc" ? (
      <ChevronUp size={16} />
    ) : (
      <ChevronDown size={16} />
    );
  };

  // Add this function for samples sorting
  const handleSamplesSort = (field: SortField) => {
    if (samplesSortField === field) {
      setSamplesSortDirection(samplesSortDirection === "asc" ? "desc" : "asc");
    } else {
      setSamplesSortField(field);
      setSamplesSortDirection("asc");
    }
  };

  const getSamplesSortIcon = (field: SortField) => {
    if (samplesSortField !== field)
      return <ChevronDown size={16} className="opacity-30" />;
    return samplesSortDirection === "asc" ? (
      <ChevronUp size={16} />
    ) : (
      <ChevronDown size={16} />
    );
  };

  // Add this function for POs sorting
  const handlePosSort = (field: SortField) => {
    if (posSortField === field) {
      setPosSortDirection(posSortDirection === "asc" ? "desc" : "asc");
    } else {
      setPosSortField(field);
      setPosSortDirection("asc");
    }
  };

  const getPosSortIcon = (field: SortField) => {
    if (posSortField !== field)
      return <ChevronDown size={16} className="opacity-30" />;
    return posSortDirection === "asc" ? (
      <ChevronUp size={16} />
    ) : (
      <ChevronDown size={16} />
    );
  };

  const handleViewDetails = (activity) => {
    if(activity.type === "meetings") {
      navigate("/crm", { 
        state: { 
          activeTab: "meetings",
          selectedCustomer: activity.customerId
        }
      });
      return;
    }
    if (activity.enquiryId) {
      window.open(`crm/?enquiryId=${activity.enquiryId}&activeTab=salesstack`, '_blank');
    } 
    if(activity.enquiryId && (activity.type === "sample_delivered" || activity.type === "sample_request_recieved" ||  activity.type === "sample_delivery_confirmation_reminder" || activity.type === "sample_delivery_confirmation_received" ||  activity.type === "testing_started_confirmation_reminder" ||  activity.type === "testing_started_confirmation" || activity.type === "sample_feedback_received" ||  activity.type === "sample_feedback_follow-up_reminder")) {
      window.open(`crm/?enquiryId=${activity.enquiryId}&sampleId=${activity.enquiryId}&activeTab=salesstack`, '_blank');
    }
  };

  const handleAddNewRemarks = (customerId: string) => {
    setEditingRemarks(customerId);
    setRemarksEditForm({
      current: "",
      previous: ""
    });
  };

  const handleEditRemarks = (customerId: string, currentRemarks: string) => {
    setEditingRemarks(customerId);
    setRemarksEditForm({
      current: currentRemarks,
      previous: currentRemarks
    });
  };

  const handleSaveRemarks = async (customerId: string) => {
    try {
      // Get the current user ID
      const {
        data: { user },
      } = await supabase.auth.getUser();
      const userId = user?.id || "";

      // Create a new history entry
      const { error: insertError } = await supabase
        .from("customer_activity_history")
        .insert({
          customer_id: customerId,
          remarks: remarksEditForm.current,
          created_at: new Date().toISOString(),
        });

      if (insertError) throw insertError;

      // Refresh activities after update
      const { data } = await supabase
      .from('customer')
      .select(`
        id,
        customer_full_name,
        activities:customer_activity!inner(
          id,
          activity_type,
          description,
          created_at,
          enquiries:enquiries(*)
        ),
        remarks_history:customer_activity_history(
          remarks,
          created_at
        )
      `)
        .eq('id', customerId);

      if (data) {
        // Sort activities by created_at in descending order
        const sortedActivities = data[0].activities.sort((a, b) => 
          new Date(b.created_at).getTime() - new Date(a.created_at).getTime()
        );
        
        // Get the latest and previous remarks from the customer's remarks history
        const latestRemarks = data[0].remarks_history[data[0].remarks_history.length - 1]?.remarks || "";
        const previousRemarks = data[0].remarks_history[data[0].remarks_history.length - 2]?.remarks || "";
        
        setActivities(
          sortedActivities.map((activity) => ({
            ...activity,
            reviewRemarksCurrent: latestRemarks,
            reviewRemarksPrevious: previousRemarks,
            remarks_history: data[0].remarks_history || []
          }))
        );
      }

      setEditingRemarks(null);
    } catch (error) {
      console.error("Error updating remarks:", error);
    }
  };

  // Unified Filter/Search UI for all tabs
  const renderFilterAndSearch = () => {
    if (activeTab === 'enquiries') {
      return (
        <div className="flex justify-between items-center mb-4">
          <div className="flex gap-2">
            <Button
              variant={enquiriesFilter === 'all' ? 'default' : 'outline'}
              size="sm"
              onClick={() => setEnquiriesFilter('all')}
            >
              All
            </Button>
            <Button
              variant={enquiriesFilter === 'open' ? 'default' : 'outline'}
              size="sm"
              onClick={() => setEnquiriesFilter('open')}
            >
              Open
            </Button>
            <Button
              variant={enquiriesFilter === 'closed' ? 'default' : 'outline'}
              size="sm"
              onClick={() => setEnquiriesFilter('closed')}
            >
              Closed
            </Button>
          </div>
          <input
            type="text"
            value={searchTerm}
            onChange={e => setSearchTerm(e.target.value)}
            placeholder="Search..."
            className="border rounded px-2 py-2 text-base w-72 ml-auto"
          />
        </div>
      );
    } else if (activeTab === 'samples') {
      return (
        <div className="flex justify-between items-center mb-4">
          <div className="flex gap-2">
            <Button
              variant={samplesFilter === 'all' ? 'default' : 'outline'}
              size="sm"
              onClick={() => setSamplesFilter('all')}
            >
              All
            </Button>
            <Button
              variant={samplesFilter === 'open' ? 'default' : 'outline'}
              size="sm"
              onClick={() => setSamplesFilter('open')}
            >
              Open
            </Button>
            <Button
              variant={samplesFilter === 'closed' ? 'default' : 'outline'}
              size="sm"
              onClick={() => setSamplesFilter('closed')}
            >
              Closed
            </Button>
          </div>
          <input
            type="text"
            value={searchTerm}
            onChange={e => setSearchTerm(e.target.value)}
            placeholder="Search..."
            className="border rounded px-2 py-2 text-base w-72 ml-auto"
          />
        </div>
      );
    } else if (activeTab === 'pos') {
      return (
        <div className="flex justify-between items-center mb-4">
          <div className="flex gap-2">
            <Button
              variant={poFilter === 'all' ? 'default' : 'outline'}
              size="sm"
              onClick={() => setPoFilter('all')}
            >
              All
            </Button>
            <Button
              variant={poFilter === 'open' ? 'default' : 'outline'}
              size="sm"
              onClick={() => setPoFilter('open')}
            >
              Open
            </Button>
            <Button
              variant={poFilter === 'delivered' ? 'default' : 'outline'}
              size="sm"
              onClick={() => setPoFilter('delivered')}
            >
              Delivered
            </Button>
          </div>
          <input
            type="text"
            value={searchTerm}
            onChange={e => setSearchTerm(e.target.value)}
            placeholder="Search..."
            className="border rounded px-2 py-2 text-base w-72 ml-auto"
          />
        </div>
      );
    }
    return null;
  };

  const getDateRange = (period: string) => {
    const now = new Date();
    const startDate = new Date();
    const endDate = new Date();
    const utcNow = new Date(Date.UTC(
      now.getUTCFullYear(),
      now.getUTCMonth(),
      now.getUTCDate(),
      now.getUTCHours(),
      now.getUTCMinutes(),
      now.getUTCSeconds()
    ));
    switch (period) {
      case "last7days":
        startDate.setUTCDate(utcNow.getUTCDate() - 7);
        startDate.setUTCHours(0, 0, 0, 0);
        endDate.setUTCHours(23, 59, 59, 999);
        break;
      case "currentWeek":
        startDate.setUTCDate(utcNow.getUTCDate() - utcNow.getUTCDay());
        startDate.setUTCHours(0, 0, 0, 0);
        endDate.setUTCHours(23, 59, 59, 999);
        break;
      case "lastWeek":
        startDate.setUTCDate(utcNow.getUTCDate() - utcNow.getUTCDay() - 7);
        startDate.setUTCHours(0, 0, 0, 0);
        endDate.setUTCDate(utcNow.getUTCDate() - utcNow.getUTCDay() - 1);
        endDate.setUTCHours(23, 59, 59, 999);
        break;
      case "currentMonth":
        startDate.setUTCDate(1);
        startDate.setUTCHours(0, 0, 0, 0);
        endDate.setUTCHours(23, 59, 59, 999);
        break;
      case "lastMonth":
        startDate.setUTCMonth(utcNow.getUTCMonth() - 1);
        startDate.setUTCDate(1);
        startDate.setUTCHours(0, 0, 0, 0);
        endDate.setUTCMonth(utcNow.getUTCMonth());
        endDate.setUTCDate(0);
        endDate.setUTCHours(23, 59, 59, 999);
        break;
      default:
        startDate.setUTCDate(utcNow.getUTCDate() - 7);
        startDate.setUTCHours(0, 0, 0, 0);
        endDate.setUTCHours(23, 59, 59, 999);
    }
    return {
      startDate: startDate.toISOString(),
      endDate: endDate.toISOString(),
    };
  };

  // Add this before rendering the Recent Activity table
  const filteredActivities = React.useMemo(() => {
    if (timeFilter === 'all') return activities;
    const { startDate, endDate } = getDateRange(timeFilter);
    return activities.filter(activity => {
      const created = new Date(activity.created_at).toISOString();
      return created >= startDate && created <= endDate;
    });
  }, [activities, timeFilter]);

  return (
    <div className="flex flex-col h-full">
      {editBasicInfo ? (
        <div className="p-4">
          <div className="flex justify-between items-center mb-4">
            <h2 className="text-xl font-semibold">Edit Customer</h2>
            <Button
              variant="ghost"
              size="icon"
              onClick={() => setEditBasicInfo(false)}
            >
              <X size={20} />
            </Button>
          </div>
          <AddCustomerForm
            onSubmit={handleCustomerUpdate}
            onCancel={() => setEditBasicInfo(false)}
            initialData={customerData}
            editMode="basic"
          />
        </div>
      ) : editContactInfo ? (
        <div className="p-4">
          <div className="flex justify-between items-center mb-4">
            <h2 className="text-xl font-semibold">Edit Contact Information</h2>
            <Button
              variant="ghost"
              size="icon"
              onClick={() => setEditContactInfo(false)}
            >
              <X size={20} />
            </Button>
          </div>
          <AddCustomerForm
            onSubmit={handleCustomerUpdate}
            onCancel={() => setEditContactInfo(false)}
            initialData={customerData}
            editMode="contact"
          />
        </div>
      ) : (
        <>
          <div className="flex items-center justify-between mb-6">
            <div className="flex items-center gap-2">
              <Button variant="ghost" size="icon" onClick={onBack}>
                <ArrowLeft size={18} />
              </Button>
              <div className="flex items-center">
                {/* <Button 
                  variant="ghost" 
                  size="icon" 
                  className="h-8 w-8"
                  onClick={() => handleSwitchCustomer('prev')}
                >
                  <ChevronLeft size={16} />
                </Button> */}
                <h2 className="text-2xl font-semibold mx-2">
                  {customerData?.customer_full_name}
                </h2>
                {/* <Button 
                  variant="ghost" 
                  size="icon" 
                  className="h-8 w-8"
                  onClick={() => handleSwitchCustomer('next')}
                >
                  <ChevronRight size={16} />
                </Button> */}
              </div>
            </div>

            <div className="flex items-center gap-2">
              <Button variant="outline" size="sm" onClick={handleViewMeetings}>
                <Calendar size={16} className="mr-1" />
                View Meetings
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={() => {
                  setAccountPlanningExpanded(true);
                  setTimeout(() => {
                    accountPlanningRef.current?.scrollIntoView({ behavior: 'smooth', block: 'start' });
                  }, 400); // Increased delay
                }}
              >
                <Briefcase size={16} className="mr-1" />
                {accountPlanningExpanded
                  ? "Hide Account Planning"
                  : "View Account Planning"}
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={() => {
                  setRecentActivityExpanded(true);
                  setTimeout(() => {
                    recentActivityRef.current?.scrollIntoView({ behavior: 'smooth', block: 'start' });
                  }, 100); // Wait for expand animation
                }}
              >
                <Clock size={16} className="mr-1" />
                View Recent Activity
              </Button>
              <Button variant="outline" size="sm" onClick={handleViewSnapshot}>
                <Eye size={16} className="mr-1" />
                View Snapshot
              </Button>
              {/* <Button variant="outline" size="sm" onClick={() => setEditBasicInfo(true)}>
                <Edit size={16} className="mr-1" />
                Edit Customer
              </Button> */}
            </div>
          </div>

          <Accordion
            type="single"
            collapsible
            className="mb-6"
            defaultValue={null}
          >
            <AccordionItem value="customer-info">
              <AccordionTrigger className="hover:no-underline py-2 text-lg font-bold">
                Customer Information
              </AccordionTrigger>
              <AccordionContent>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <Card>
                    <CardHeader className="pb-2">
                      <div className="flex justify-between">
                        <CardTitle className="text-sm font-semibold text-muted-foreground">
                          Basic Information
                        </CardTitle>
                        <Button
                          variant="ghost"
                          size="icon"
                          className="h-7 w-7"
                          onClick={() => setEditBasicInfo(true)}
                        >
                          <Edit size={14} />
                        </Button>
                      </div>
                    </CardHeader>
                    <CardContent>
                      <div className="grid grid-cols-2 gap-4">
                        <div className="space-y-2">
                          <div className="flex items-start">
                            <MapPin
                              size={16}
                              className="mr-2 mt-0.5 text-muted-foreground shrink-0"
                            />
                            <div>
                              {/* <div className="text-sm font-medium">Location</div> */}
                              <div className="text-sm">
                                {customerData?.city}, {customerData?.country}
                              </div>
                            </div>
                          </div>

                          <div className="flex items-start">
                            <BarChart
                              size={16}
                              className="mr-2 mt-0.5 text-muted-foreground shrink-0"
                            />
                            <div>
                              <div className="text-sm font-medium">
                                Annual Turnover (Million $)
                              </div>
                              <div className="text-sm">
                                {customerData?.annual_turnover}
                              </div>
                            </div>
                          </div>

                          <div className="flex items-start">
                            <Users
                              size={16}
                              className="mr-2 mt-0.5 text-muted-foreground shrink-0"
                            />
                            <div>
                              <div className="text-sm font-medium">
                                Account Owner
                              </div>
                              <div className="text-sm">
                                {customerData?.account_owner}
                              </div>
                            </div>
                          </div>
                        </div>

                        <div className="space-y-2">
                          <div className="flex items-start">
                            <Layers
                              size={16}
                              className="mr-2 mt-0.5 text-muted-foreground shrink-0"
                            />
                            <div>
                              <div className="text-sm font-medium">
                                Industries
                              </div>
                              <div className="flex flex-wrap gap-1 mt-1">
                                {customerData?.industries?.map(
                                  (industry, index) => (
                                    <div
                                      key={index}
                                      className="badge badge-secondary"
                                    >
                                      {industry}
                                    </div>
                                  )
                                )}
                              </div>
                            </div>
                          </div>

                          <div className="flex items-start">
                            <BarChart
                              size={16}
                              className="mr-2 mt-0.5 text-muted-foreground shrink-0"
                            />
                            <div>
                              <div className="text-sm font-medium">
                                Annual Chemicals Procurement ($)
                              </div>
                              <div className="text-sm">
                                {customerData?.annual_chemicals_procurement}
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    </CardContent>
                  </Card>

                  <Card className="md:ml-2">
                    <CardHeader className="pb-2">
                      <div className="flex justify-between">
                        <CardTitle className="text-sm font-semibold text-muted-foreground">
                          Contact Information
                        </CardTitle>
                        <Button
                          variant="ghost"
                          size="icon"
                          className="h-7 w-7"
                          onClick={() => setEditContactInfo(true)}
                        >
                          <Edit size={14} />
                        </Button>
                      </div>
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-4">
                        {customerData?.contacts?.map((contact, index) => (
                          <div
                            key={index}
                            className="pb-2 border-b last:border-b-0 last:pb-0"
                          >
                            <div className="flex items-start mb-1">
                              <Users
                                size={16}
                                className="mr-2 mt-0.5 text-muted-foreground shrink-0"
                              />
                              <div>
                                <div className="text-sm font-medium">
                                  {contact.name}
                                </div>
                                <div className="text-xs text-muted-foreground">
                                  {contact.role}
                                </div>
                              </div>
                            </div>
                            <div className="flex items-center ml-6 mt-1">
                              <Mail
                                size={14}
                                className="mr-2 text-muted-foreground shrink-0"
                              />
                              <div className="text-sm">{contact.email}</div>
                            </div>
                            <div className="flex items-center ml-6 mt-1">
                              <Phone
                                size={14}
                                className="mr-2 text-muted-foreground shrink-0"
                              />
                              <div className="text-sm">{contact.phone}</div>
                            </div>
                          </div>
                        ))}
                      </div>
                    </CardContent>
                  </Card>
                </div>
              </AccordionContent>
            </AccordionItem>
          </Accordion>

          <Card className="mb-6">
            <CardContent className="p-4">
              <div className="flex justify-between items-center mb-3">
                <h3 className="text-sm font-bold">Metrics</h3>
                <Select value={metricsPeriod} onValueChange={setMetricsPeriod}>
                  <SelectTrigger className="w-[180px] h-8">
                    <SelectValue placeholder="Select period" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="currentQuarter">
                      Current Quarter
                    </SelectItem>
                    <SelectItem value="previousQuarter">
                      Previous Quarter
                    </SelectItem>
                    <SelectItem value="ytd">YTD</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div className="grid grid-cols-7 gap-2">
                <div>
                  <div className="text-xs text-muted-foreground font-semibold">
                    Target
                  </div>
                  {metricsLoading ? (
                    <Loader2 className="h-4 w-4 animate-spin m-2 center" />
                  ) : (
                    <div className="text-lg font-semibold">
                      {metrics?.sum_target
                        ? `$${metrics?.sum_target?.toLocaleString()}`
                        : "N/A"}
                    </div>
                  )}
                </div>
                <div>
                  <div className="text-xs text-muted-foreground font-semibold">
                    Revenue Booked
                  </div>
                  {metricsLoading ? (
                    <Loader2 className="h-4 w-4 animate-spin m-2 center" />
                  ) : (
                    <div className="text-lg font-semibold">
                      {metrics?.total_revenue_booked
                        ? `$${metrics?.total_revenue_booked?.toLocaleString()}`
                        : "N/A"}
                    </div>
                  )}
                </div>
                <div>
                  <div className="text-xs text-muted-foreground font-semibold">
                    Revenue Realized
                  </div>
                  {metricsLoading ? (
                    <Loader2 className="h-4 w-4 animate-spin m-2 center" />
                  ) : (
                    <div className="text-lg font-semibold">
                      {metrics?.total_revenue
                        ? `$${metrics?.total_revenue?.toLocaleString()}`
                        : "N/A"}
                    </div>
                  )}
                </div>
                {isAdmin && (
                  <div>
                    <div className="text-xs text-muted-foreground font-semibold">
                      Margin %
                    </div>
                    {metricsLoading ? (
                      <Loader2 className="h-4 w-4 animate-spin m-2 center" />
                    ) : (
                      <div className="text-lg font-semibold">
                        {metrics?.margin_percentage
                          ? `${metrics?.margin_percentage}%`
                          : "N/A"}
                      </div>
                    )}
                  </div>
                )}
                <div>
                  <div className="text-xs text-muted-foreground font-semibold">
                    Procurement Mapped
                  </div>
                  {metricsLoading ? (
                    <Loader2 className="h-4 w-4 animate-spin m-2 center" />
                  ) : (
                    <div className="text-lg font-semibold">
                      {metrics?.procurement_percentage
                        ? `${metrics?.procurement_percentage}%`
                        : "N/A"}
                    </div>
                  )}
                </div>
                <div>
                  <div className="text-xs text-muted-foreground font-semibold">
                    Share of Wallet
                  </div>
                  {metricsLoading ? (
                    <Loader2 className="h-4 w-4 animate-spin m-2 center" />
                  ) : (
                    <div className="text-lg font-semibold">
                      {metrics?.share_wallet
                        ? `${metrics.share_wallet}%`
                        : "N/A"}
                    </div>
                  )}
                </div>
                <div>
                  <div className="text-xs text-muted-foreground font-semibold">
                    Repeat Rate
                  </div>
                  {metricsLoading ? (
                    <Loader2 className="h-4 w-4 animate-spin m-2 center" />
                  ) : (
                    <div className="text-lg font-semibold">
                      {metrics?.repeated_rate
                        ? `${metrics.repeated_rate}%`
                        : "N/A"}
                    </div>
                  )}
                </div>
              </div>
            </CardContent>
          </Card>
          {loading ? (
            <Card className="flex-1 mb-6">
              <CardHeader>
                <div className="h-6 w-32 bg-gray-200 rounded animate-pulse"></div>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {/* Tabs skeleton */}
                  <div className="flex justify-between items-center mb-4">
                    <div className="flex gap-2">
                      <div className="h-8 w-24 bg-gray-200 rounded animate-pulse"></div>
                      <div className="h-8 w-24 bg-gray-200 rounded animate-pulse"></div>
                      <div className="h-8 w-24 bg-gray-200 rounded animate-pulse"></div>
                    </div>
                    <div className="h-8 w-32 bg-gray-200 rounded animate-pulse"></div>
                  </div>

                  {/* Table skeleton */}
                  <div className="rounded-md border">
                    <div className="h-10 bg-gray-100 border-b"></div>
                    {[1, 2, 3].map((_, index) => (
                      <div
                        key={index}
                        className="h-12 border-b last:border-b-0"
                      >
                        <div className="grid grid-cols-7 gap-4 p-3">
                          <div className="h-4 bg-gray-200 rounded animate-pulse"></div>
                          <div className="h-4 bg-gray-200 rounded animate-pulse"></div>
                          <div className="h-4 bg-gray-200 rounded animate-pulse"></div>
                          <div className="h-4 bg-gray-200 rounded animate-pulse"></div>
                          <div className="h-4 bg-gray-200 rounded animate-pulse"></div>
                          <div className="h-4 bg-gray-200 rounded animate-pulse"></div>
                          <div className="h-4 bg-gray-200 rounded animate-pulse"></div>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              </CardContent>
            </Card>
          ) : (
            <Card className="flex-1 mb-6">
              <CardHeader>
                <CardTitle>Projects</CardTitle>
              </CardHeader>
              <CardContent>
                <Tabs
                  defaultValue="enquiries"
                  className="w-full"
                  onValueChange={(value) => setActiveTab(value)}
                >
                  <div className="flex justify-between items-center mb-4">
                    <TabsList>
                      <TabsTrigger value="enquiries" className="font-semibold">
                        Enquiries ({enquiries.length})
                      </TabsTrigger>
                      <TabsTrigger value="samples" className="font-semibold">
                        Samples ({samples.length})
                      </TabsTrigger>
                      <TabsTrigger value="pos" className="font-semibold">
                        Purchase Orders ({purchaseOrders.length})
                      </TabsTrigger>
                    </TabsList>
                    {/* <div className="flex items-center gap-2">
                      {activeTab !== "pos" && (
                        <Select
                          value={
                            activeTab === "enquiries"
                              ? enquiriesFilter
                              : activeTab === "samples"
                              ? samplesFilter
                              : posFilter
                          }
                          onValueChange={(value) => {
                            if (activeTab === "enquiries") {
                              setEnquiriesFilter(value);
                            } else if (activeTab === "samples") {
                              setSamplesFilter(value);
                            } else {
                              setPosFilter(value);
                            }
                          }}
                        >
                          <SelectTrigger className="w-[130px] h-8">
                            <SelectValue placeholder="Filter" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="all">All</SelectItem>
                            <SelectItem value="open">Open</SelectItem>
                            <SelectItem value="closed">Closed</SelectItem>
                          </SelectContent>
                        </Select>
                      )}
                    </div> */}
                  </div>

                  {renderFilterAndSearch()}

                  <TabsContent value="enquiries" className="mt-4">
                    <div className="rounded-md border">
                      <Table>
                        <TableHeader>
                          <TableRow>
                            <TableHead className="font-semibold">
                              ENQ Number
                            </TableHead>
                            <TableHead className="font-semibold">
                              Chemical Name
                            </TableHead>
                            <TableHead className="font-semibold">
                              Current State
                            </TableHead>
                            <TableHead
                              className="font-semibold cursor-pointer"
                              onClick={() => handleSort("criticality")}
                            >
                              <div className="flex items-center gap-1">
                                Criticality
                                {getSortIcon("criticality")}
                              </div>
                            </TableHead>
                            <TableHead
                              className="font-semibold cursor-pointer"
                              onClick={() => handleSort("potentialOrderValue")}
                            >
                              <div className="flex items-center gap-1">
                                Potential Order Value
                                {getSortIcon("potentialOrderValue")}
                              </div>
                            </TableHead>
                            <TableHead
                              className="font-semibold cursor-pointer"
                              onClick={() =>
                                handleSort("expectedQuarterlyProcurement")
                              }
                            >
                              <div className="flex items-center gap-1">
                                Exp. Quarterly Procurement
                                {getSortIcon("expectedQuarterlyProcurement")}
                              </div>
                            </TableHead>
                            {isAdmin && (
                              <TableHead className="font-semibold">
                                Margin
                              </TableHead>
                            )}
                            <TableHead
                              className="font-semibold cursor-pointer"
                              onClick={() => handleSort("requestDate")}
                            >
                              <div className="flex items-center gap-1">
                                Request Date
                                {getSortIcon("requestDate")}
                              </div>
                            </TableHead>
                            <TableHead
                              className="font-semibold cursor-pointer"
                              onClick={() => handleSort("closureDate")}
                            >
                              <div className="flex items-center gap-1">
                                Closure Date
                                {getSortIcon("closureDate")}
                              </div>
                            </TableHead>
                            <TableHead className="font-semibold">
                              Quotation Feedback
                            </TableHead>
                          </TableRow>
                        </TableHeader>
                        <TableBody>
                          {paginatedEnquiries.map((item) => (
                            <TableRow key={item.id}>
                              <TableCell>
                                {item.enquiry_id ? (
                                  <Button
                                    variant="link"
                                    className="p-0 h-auto font-normal text-[#294d48] hover:text-[#1a2f2c] hover:underline"
                                    onClick={() => window.open(`crm/?enquiryId=${item.enquiry_id}&activeTab=salesstack`, '_blank')}
                                  >
                                    {item.enquiry_id}
                                  </Button>
                                ) : (
                                  "N/A"
                                )}
                              </TableCell>
                              <TableCell>{item.chemical_name}</TableCell>
                              <TableCell>
                                {item.current_status
                                  ? item.current_status
                                      .replace(/_/g, " ")
                                      .replace(/\b\w/g, (l) => l.toUpperCase())
                                  : "N/A"}
                              </TableCell>
                              <TableCell>
                                <Badge
                                  className={`${
                                    item.confidence === "high"
                                      ? "bg-red-100 text-red-800"
                                      : item.confidence === "medium"
                                      ? "bg-yellow-100 text-yellow-800"
                                      : "bg-green-100 text-green-800"
                                  }`}
                                >
                                  {item.confidence
                                    ? item.confidence.charAt(0).toUpperCase() +
                                      item.confidence.slice(1)
                                    : "Low"}
                                </Badge>
                              </TableCell>
                              <TableCell>{item.potentialOrderValue}</TableCell>
                              <TableCell>
                                {item.expectedQuarterlyProcurement}
                              </TableCell>
                              {isAdmin && <TableCell>{item.margin}</TableCell>}
                              <TableCell>{item.requestDate}</TableCell>
                              <TableCell>{item.closureDate || "-"}</TableCell>
                              <TableCell>
                                {item.quotationFeedback || "-"}
                              </TableCell>
                            </TableRow>
                          ))}
                        </TableBody>
                      </Table>
                      {getTotalPages("enquiries") > 1 && (
                        <div className="flex items-center justify-end space-x-2 py-4 px-4">
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() =>
                              setEnquiriesPage((prev) => Math.max(prev - 1, 1))
                            }
                            disabled={enquiriesPage === 1}
                          >
                            <ChevronLeft className="h-4 w-4" />
                          </Button>
                          <div className="text-sm text-muted-foreground">
                            Page {enquiriesPage} of {getTotalPages("enquiries")}
                          </div>
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() =>
                              setEnquiriesPage((prev) =>
                                Math.min(prev + 1, getTotalPages("enquiries"))
                              )
                            }
                            disabled={
                              enquiriesPage === getTotalPages("enquiries")
                            }
                          >
                            <ChevronRight className="h-4 w-4" />
                          </Button>
                        </div>
                      )}
                    </div>
                  </TabsContent>

                  <TabsContent value="samples" className="mt-4">
                    <div className="rounded-md border">
                      <Table>
                        <TableHeader>
                          <TableRow>
                            <TableHead className="font-semibold">
                              ENQ Number
                            </TableHead>
                            <TableHead className="font-semibold">
                              Chemical Name
                            </TableHead>
                            <TableHead className="font-semibold">
                              Current State
                            </TableHead>
                            <TableHead
                              className="font-semibold cursor-pointer"
                              onClick={() => handleSamplesSort("criticality")}
                            >
                              <div className="flex items-center gap-1">
                                Criticality
                                {getSamplesSortIcon("criticality")}
                              </div>
                            </TableHead>
                            <TableHead
                              className="font-semibold cursor-pointer"
                              onClick={() => handleSamplesSort("eta")}
                            >
                              <div className="flex items-center gap-1">
                                ETA
                                {getSamplesSortIcon("eta")}
                              </div>
                            </TableHead>
                            <TableHead
                              className="font-semibold cursor-pointer"
                              onClick={() =>
                                handleSamplesSort("potentialOrderValue")
                              }
                            >
                              <div className="flex items-center gap-1">
                                Potential Order Value
                                {getSamplesSortIcon("potentialOrderValue")}
                              </div>
                            </TableHead>
                            <TableHead
                              className="font-semibold cursor-pointer"
                              onClick={() =>
                                handleSamplesSort(
                                  "expectedQuarterlyProcurement"
                                )
                              }
                            >
                              <div className="flex items-center gap-1">
                                Exp. Quarterly Procurement
                                {getSamplesSortIcon(
                                  "expectedQuarterlyProcurement"
                                )}
                              </div>
                            </TableHead>
                            {isAdmin && (
                              <TableHead className="font-semibold">
                                Margin
                              </TableHead>
                            )}
                            <TableHead
                              className="font-semibold cursor-pointer"
                              onClick={() => handleSamplesSort("requestDate")}
                            >
                              <div className="flex items-center gap-1">
                                Request Date
                                {getSamplesSortIcon("requestDate")}
                              </div>
                            </TableHead>
                            <TableHead
                              className="font-semibold cursor-pointer"
                              onClick={() => handleSamplesSort("closureDate")}
                            >
                              <div className="flex items-center gap-1">
                                Closure Date
                                {getSamplesSortIcon("closureDate")}
                              </div>
                            </TableHead>
                            <TableHead className="font-semibold">
                              Sample Feedback
                            </TableHead>
                          </TableRow>
                        </TableHeader>
                        <TableBody>
                          {paginatedSamples.map((item) => (
                            <TableRow key={item.id}>
                              <TableCell>
                                {item.enquiryId ? (
                                  <Button
                                    variant="link"
                                    className="p-0 h-auto font-normal text-[#294d48] hover:text-[#1a2f2c] hover:underline"
                                    onClick={() => window.open(`crm/?enquiryId=${item.enquiryId}&sampleId=${item.enquiryId}&activeTab=salesstack`, '_blank')}
                                  >
                                    {item.enquiryId}
                                  </Button>
                                ) : (
                                  "N/A"
                                )}
                              </TableCell>
                              <TableCell>{item.chemical_name}</TableCell>
                              <TableCell>
                                {item.current_status
                                  ? item.current_status
                                      .replace(/_/g, " ")
                                      .replace(/\b\w/g, (l) => l.toUpperCase())
                                  : "N/A"}
                              </TableCell>
                              <TableCell>
                                <Badge
                                  className={`${
                                    item.confidence === "high"
                                      ? "bg-red-100 text-red-800"
                                      : item.confidence === "medium"
                                      ? "bg-yellow-100 text-yellow-800"
                                      : "bg-green-100 text-green-800"
                                  }`}
                                >
                                  {item.confidence
                                    ? item.confidence.charAt(0).toUpperCase() +
                                      item.confidence.slice(1)
                                    : ""}
                                </Badge>
                              </TableCell>
                              <TableCell>
                                {item.eta || "-"}
                                {item.tracking_url && (
                                  <Button
                                    variant="link"
                                    className="p-0 h-auto font-normal text-[#294d48] hover:text-[#1a2f2c] hover:underline ml-1"
                                    onClick={() => window.open(item.tracking_url, '_blank')}
                                  >
                                    (link)
                                  </Button>
                                )}
                              </TableCell>
                              <TableCell>{item.potentialOrderValue}</TableCell>
                              <TableCell>
                                {item.expectedQuarterlyProcurement}
                              </TableCell>
                              {isAdmin && <TableCell>{item.margin}</TableCell>}
                              <TableCell>{item.requestDate}</TableCell>
                              <TableCell>{item.closureDate || "-"}</TableCell>
                              <TableCell>
                                {item.sampleFeedback || "-"}
                              </TableCell>
                            </TableRow>
                          ))}
                        </TableBody>
                      </Table>
                      {getTotalPages("samples") > 1 && (
                        <div className="flex items-center justify-end space-x-2 py-4 px-4">
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() =>
                              setSamplesPage((prev) => Math.max(prev - 1, 1))
                            }
                            disabled={samplesPage === 1}
                          >
                            <ChevronLeft className="h-4 w-4" />
                          </Button>
                          <div className="text-sm text-muted-foreground">
                            Page {samplesPage} of {getTotalPages("samples")}
                          </div>
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() =>
                              setSamplesPage((prev) =>
                                Math.min(prev + 1, getTotalPages("samples"))
                              )
                            }
                            disabled={samplesPage === getTotalPages("samples")}
                          >
                            <ChevronRight className="h-4 w-4" />
                          </Button>
                        </div>
                      )}
                    </div>
                  </TabsContent>

                  <TabsContent value="pos" className="mt-4">
                    <div className="rounded-md border">
                      <Table>
                        <TableHeader>
                          <TableRow>
                            <TableHead className='font-semibold'>PO Number</TableHead>
                            <TableHead className='font-semibold'>Order Value ($)</TableHead>
                            <TableHead className='font-semibold'>Order Date</TableHead>
                            <TableHead className='font-semibold'>Frequency days</TableHead>
                            <TableHead className='font-semibold'>Chemical Name</TableHead>
                            <TableHead className='font-semibold'>Current State</TableHead>
                            <TableHead className='font-semibold'>ETA</TableHead>
                            <TableHead className='font-semibold'>Sales Order Value ($)</TableHead>
                            <TableHead className='font-semibold'>Expected Quarterly Procurement</TableHead>
                            {isAdmin && <TableHead className='font-semibold'>Margin</TableHead>}
                            <TableHead className='font-semibold'>Delivery Date</TableHead>
                            <TableHead className='font-semibold'>Payment Terms</TableHead>
                          </TableRow>
                        </TableHeader>
                        <TableBody>
                          {paginatedPOs.map(([poNumber, po]) => (
                            <React.Fragment key={poNumber}>
                              {po.chemicals.map((chem, index) => (
                                <TableRow key={`${poNumber}-${index}`}>
                                  {index === 0 && (
                                    <>
                                      <TableCell rowSpan={po.chemicals.length}>
                                        <Button
                                          variant="link"
                                          className="p-0 h-auto font-normal text-[#294d48] hover:text-[#1a2f2c] hover:underline"
                                        >
                                          {po.poNumber}
                                        </Button>
                                      </TableCell>
                                      <TableCell rowSpan={po.chemicals.length}>{po.orderValue}</TableCell>
                                      <TableCell rowSpan={po.chemicals.length}>{po.orderDate}</TableCell>
                                      <TableCell rowSpan={po.chemicals.length}>{po.repeatFrequency}</TableCell>
                                    </>
                                  )}
                                  <TableCell>{chem.chemical_name}</TableCell>
                                  <TableCell>{chem.current_status}</TableCell>
                                  <TableCell>{chem.eta}</TableCell>
                                  <TableCell>{chem.salesOrderValue}</TableCell>
                                  <TableCell>{chem.expectedQuarterlyProcurement}</TableCell>
                                  {isAdmin && <TableCell>{chem.margin}</TableCell>}
                                  <TableCell>{chem.deliveryDate}</TableCell>
                                  <TableCell>{chem.paymentTerms}</TableCell>
                                </TableRow>
                              ))}
                            </React.Fragment>
                          ))}
                        </TableBody>
                      </Table>
                      {getTotalPages("pos") > 1 && (
                        <div className="flex items-center justify-end space-x-2 py-4 px-4">
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() =>
                              setPosPage((prev) => Math.max(prev - 1, 1))
                            }
                            disabled={posPage === 1}
                          >
                            <ChevronLeft className="h-4 w-4" />
                          </Button>
                          <div className="text-sm text-muted-foreground">
                            Page {posPage} of {getTotalPages("pos")}
                          </div>
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() =>
                              setPosPage((prev) =>
                                Math.min(prev + 1, getTotalPages("pos"))
                              )
                            }
                            disabled={posPage === getTotalPages("pos")}
                          >
                            <ChevronRight className="h-4 w-4" />
                          </Button>
                        </div>
                      )}
                    </div>
                  </TabsContent>
                </Tabs>
              </CardContent>
            </Card>
          )}

          <div ref={accountPlanningRef}>
            <Collapsible
              open={accountPlanningExpanded}
              onOpenChange={setAccountPlanningExpanded}
              className="mb-6 border rounded-lg p-3"
            >
              <div className="flex justify-between items-center">
                <h3 className="text-lg font-semibold">Account Planning</h3>
                <CollapsibleTrigger asChild>
                  <Button variant="ghost" size="sm">
                    {accountPlanningExpanded ? (
                      <ChevronUp className="h-4 w-4" />
                    ) : (
                      <ChevronDown className="h-4 w-4" />
                    )}
                  </Button>
                </CollapsibleTrigger>
              </div>

              <CollapsibleContent className="pt-4">
                <div className="space-y-6">
                  <div>
                    <div className="flex items-center justify-between mb-2">
                      <h3 className="text-sm font-medium">
                        Top chemicals (or ~80% of procurement)
                      </h3>
                      <Button
                        onClick={handleAddChemical}
                        size="sm"
                        className="h-8 px-2 flex items-center gap-1"
                      >
                        <Plus className="h-3.5 w-3.5" />
                        Add Chemical
                      </Button>
                    </div>
                    <div className="overflow-x-auto">
                      <Table>
                        <TableHeader>
                          <TableRow>
                            <TableHead className="w-10 text-xs font-semibold">
                              S.No
                            </TableHead>
                            <TableHead className="w-5 text-xs font-semibold">
                              Chemical name
                            </TableHead>
                            <TableHead className="text-xs font-semibold">
                              Grade
                            </TableHead>
                            <TableHead className="text-xs font-semibold">
                              Quarterly volume
                            </TableHead>
                            <TableHead className="text-xs font-semibold">
                              Quarterly Value (USD)
                            </TableHead>
                            <TableHead className="text-xs font-semibold">
                              Achievement Value (Q-1) (USD)
                            </TableHead>
                            <TableHead className="text-xs font-semibold">
                              Target (Q) (USD)
                            </TableHead>
                            <TableHead className="text-xs font-semibold">
                              Achievement (Q) (USD)
                            </TableHead>
                            {isAdmin && (
                              <TableHead className="text-xs font-semibold">
                                Margin
                              </TableHead>
                            )}
                            <TableHead className="text-xs font-semibold">
                              Conversion probability
                            </TableHead>
                            <TableHead className="text-xs font-semibold">
                              Status
                            </TableHead>
                            <TableHead className="text-xs font-semibold">
                              Latest Comments
                            </TableHead>
                            <TableHead className="w-20 text-xs font-semibold">
                              Action
                            </TableHead>
                          </TableRow>
                        </TableHeader>
                        <TableBody>
                          {chemicals.map((chemical, index) => (
                            <TableRow
                              key={chemical.id}
                              className={
                                editingId === chemical.id ? "bg-muted/30" : ""
                              }
                            >
                              <TableCell className="text-sm">
                                {index + 1}
                              </TableCell>

                              <TableCell>
                                {editingId === chemical.id ? (
                                  <Input
                                    value={chemical.chemical_name || ""}
                                    onChange={(e) =>
                                      handleFieldChange(
                                        chemical.id,
                                        "chemical_name",
                                        e.target.value
                                      )
                                    }
                                    className="h-8 text-sm transition-all duration-200 focus:w-[200px] w-[120px]"
                                  />
                                ) : (
                                  <span className="text-sm">
                                    {chemical.chemical_name}
                                  </span>
                                )}
                              </TableCell>

                              <TableCell>
                                {editingId === chemical.id ? (
                                  <Input
                                    value={chemical.grade || ""}
                                    onChange={(e) =>
                                      handleFieldChange(
                                        chemical.id,
                                        "grade",
                                        e.target.value
                                      )
                                    }
                                    className="h-8 text-sm transition-all duration-200 focus:w-[150px] w-[80px]"
                                  />
                                ) : (
                                  <span className="text-sm">
                                    {chemical.grade}
                                  </span>
                                )}
                              </TableCell>

                              <TableCell>
                                {editingId === chemical.id ? (
                                  <div className="flex items-center gap-2">
                                    <Input
                                      type="number"
                                      value={chemical.quarterly_volume || ""}
                                      onChange={(e) =>
                                        handleFieldChange(
                                          chemical.id,
                                          "quarterly_volume",
                                          parseFloat(e.target.value)
                                        )
                                      }
                                      className="h-8 text-sm transition-all duration-200 focus:w-[120px] w-[80px]"
                                    />
                                    <Select
                                      value={chemical.quarterly_volume_unit || ""}
                                      onValueChange={(value) =>
                                        handleFieldChange(
                                          chemical.id,
                                          "quarterly_volume_unit",
                                          value
                                        )
                                      }
                                    >
                                      <SelectTrigger className="h-8 w-[80px] text-sm">
                                        <SelectValue placeholder="Unit" />
                                      </SelectTrigger>
                                      <SelectContent>
                                        <SelectItem value="Metric Ton (mt)">
                                          Mt
                                        </SelectItem>
                                        <SelectItem value="Pound (lb)">
                                          Pound
                                        </SelectItem>
                                        <SelectItem value="Gallon (gal)">
                                          Gallon
                                        </SelectItem>
                                        <SelectItem value="Litre (L)">
                                          Litre
                                        </SelectItem>
                                        <SelectItem value="Kilolitre (Kl)">
                                          Kl
                                        </SelectItem>
                                        <SelectItem value="Kilogram (Kg)">
                                          Kg
                                        </SelectItem>
                                      </SelectContent>
                                    </Select>
                                  </div>
                                ) : (
                                  <span className="text-sm">
                                    {chemical.quarterly_volume
                                      ? `${
                                          chemical.quarterly_volume
                                        } ${getUnitDisplayName(
                                          chemical.quarterly_volume_unit
                                        )}`
                                      : ""}
                                  </span>
                                )}
                              </TableCell>

                              <TableCell>
                                {editingId === chemical.id ? (
                                  <Input
                                    type="number"
                                    value={chemical.quarterly_value || ""}
                                    onChange={(e) =>
                                      handleFieldChange(
                                        chemical.id,
                                        "quarterly_value",
                                        parseFloat(e.target.value)
                                      )
                                    }
                                    className="h-8 text-sm transition-all duration-200 focus:w-[120px] w-[80px]"
                                  />
                                ) : (
                                  <span className="text-sm">
                                    {chemical.quarterly_value}
                                  </span>
                                )}
                              </TableCell>

                              <TableCell>
                                <span className="text-sm">
                                  {chemical.achievement_value_previous}
                                </span>
                              </TableCell>

                              <TableCell>
                                {editingId === chemical.id ? (
                                  <Input
                                    type="number"
                                    value={chemical.target_this_quarter || ""}
                                    onChange={(e) =>
                                      handleFieldChange(
                                        chemical.id,
                                        "target_this_quarter",
                                        parseFloat(e.target.value)
                                      )
                                    }
                                    className="h-8 text-sm transition-all duration-200 focus:w-[120px] w-[80px]"
                                  />
                                ) : (
                                  <span className="text-sm">
                                    {chemical.target_this_quarter}
                                  </span>
                                )}
                              </TableCell>

                              <TableCell>
                                <span className="text-sm">
                                  {chemical.achievement_value_current}
                                </span>
                              </TableCell>

                              <TableCell>
                                {isAdmin &&
                                  (editingId === chemical.id ? (
                                    <Input
                                      type="number"
                                      value={chemical.margin || ""}
                                      onChange={(e) =>
                                        handleFieldChange(
                                          chemical.id,
                                          "margin",
                                          parseFloat(e.target.value)
                                        )
                                      }
                                      className="h-8 text-sm transition-all duration-200 focus:w-[120px] w-[80px]"
                                    />
                                  ) : (
                                    <span className="text-sm">
                                      {chemical.margin}
                                    </span>
                                  ))}
                              </TableCell>

                              <TableCell>
                                {editingId === chemical.id ? (
                                  <div className="flex items-center gap-1">
                                    <Input
                                      type="number"
                                      min="0"
                                      max="100"
                                      value={chemical.conversion_probability || ""}
                                      onChange={(e) => {
                                        const value = parseFloat(e.target.value);
                                        if(value < 0) {
                                          toast.error(
                                            "Conversion probability cannot be less than 0%"
                                          );
                                          return;
                                        }
                                        if (value > 100) {
                                          toast.error(
                                            "Conversion probability cannot exceed 100%"
                                          );
                                          return;
                                        }
                                        handleFieldChange(
                                          chemical.id,
                                          "conversion_probability",
                                          value
                                        );
                                      }}
                                      className="h-8 text-sm transition-all duration-200 focus:w-[100px] w-[60px]"
                                    />
                                    <span className="text-sm text-muted-foreground">%</span>
                                  </div>
                                ) : (
                                  <span className="text-sm">
                                    {chemical.conversion_probability
                                      ? `${chemical.conversion_probability}%`
                                      : ""}
                                  </span>
                                )}
                              </TableCell>

                              <TableCell>
                                <Select
                                  value={chemical.status || ""}
                                  onValueChange={(value) =>
                                    handleFieldChange(
                                      chemical.id,
                                      "status",
                                      value
                                    )
                                  }
                                  disabled={editingId !== chemical.id}
                                >
                                  <SelectTrigger className="h-8 text-sm transition-all duration-200 focus:w-[250px] w-[150px]">
                                    <SelectValue placeholder="Select status" />
                                  </SelectTrigger>
                                  <SelectContent>
                                    {statusOptions.map((option) => (
                                      <SelectItem key={option} value={option}>
                                        {option}
                                      </SelectItem>
                                    ))}
                                  </SelectContent>
                                </Select>
                              </TableCell>

                              <TableCell>
                                {editingId === chemical.id ? (
                                  <Input
                                    value={chemical.comments || ""}
                                    onChange={(e) =>
                                      handleFieldChange(
                                        chemical.id,
                                        "comments",
                                        e.target.value
                                      )
                                    }
                                    className="h-8 text-sm transition-all duration-200 focus:w-[300px] w-[150px]"
                                  />
                                ) : (
                                  <span className="text-sm">
                                    {chemical.comments}
                                  </span>
                                )}
                              </TableCell>

                              <TableCell>
                                <div className="flex gap-1 justify-end">
                                  <Button
                                    variant="ghost"
                                    size="sm"
                                    className="h-7 w-7 p-0"
                                    onClick={() => toggleEdit(chemical.id)}
                                  >
                                    {editingId === chemical.id ? (
                                      <Check className="h-3.5 w-3.5" />
                                    ) : (
                                      <PenSquare className="h-3.5 w-3.5" />
                                    )}
                                  </Button>
                                  <Button
                                    variant="ghost"
                                    size="sm"
                                    className="h-7 w-7 p-0 text-destructive hover:text-destructive hover:bg-destructive/10"
                                    onClick={() =>
                                      handleDeleteChemical(chemical.id)
                                    }
                                  >
                                    <Trash2 className="h-3.5 w-3.5" />
                                  </Button>
                                </div>
                              </TableCell>
                            </TableRow>
                          ))}
                        </TableBody>
                      </Table>
                    </div>
                  </div>

                  <div>
                    <h3 className="text-sm font-semibold mb-3">
                      Summary - Pipeline
                    </h3>
                    <div className="space-y-3">
                      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 xl:grid-cols-7 gap-3">
                        {pipelineData.slice(0, 7).map((item) => (
                          <div key={item.id} className="border rounded-md p-3">
                            <div className="text-xs text-muted-foreground font-semibold">
                              {item.status}
                            </div>
                            <div className="flex justify-between items-center mt-2">
                              <span className="text-sm">
                                {item.products} products
                              </span>
                              <span className="text-lg font-medium">
                                {item.targetSum !== "0" ? item.targetSum : "0"}
                              </span>
                            </div>
                          </div>
                        ))}
                      </div>

                      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-6 gap-3">
                        {pipelineData.slice(7).map((item) => (
                          <div key={item.id} className="border rounded-md p-3">
                            <div className="text-xs text-muted-foreground">
                              {item.status}
                            </div>
                            <div className="flex justify-between items-center mt-2">
                              <span className="text-sm">
                                {item.products} products
                              </span>
                              <span className="text-lg font-medium">
                                {item.targetSum !== "0" ? item.targetSum : "0"}
                              </span>
                            </div>
                          </div>
                        ))}
                      </div>
                    </div>
                  </div>
                </div>
              </CollapsibleContent>
            </Collapsible>
          </div>

          {/* Add Recent Activity Section */}
          <div ref={recentActivityRef}>
            <Collapsible
              open={recentActivityExpanded}
              onOpenChange={setRecentActivityExpanded}
              className="mb-6 border rounded-lg p-3"
            >
              <div className="flex justify-between items-center">
                <h3 className="text-lg font-semibold">Recent Activity</h3>
                <div className="flex items-center gap-4">
                  <div className="w-48">
                    <Select value={timeFilter} onValueChange={setTimeFilter}>
                      <SelectTrigger className="h-8">
                        <SelectValue placeholder="Time Period" />
                      </SelectTrigger>
                      <SelectContent>
                        {timeFilterOptions.map(option => (
                          <SelectItem key={option.value} value={option.value}>
                            {option.label}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                  <CollapsibleTrigger asChild>
                    <Button variant="ghost" size="sm">
                      {recentActivityExpanded ? (
                        <ChevronUp className="h-4 w-4" />
                      ) : (
                        <ChevronDown className="h-4 w-4" />
                      )}
                    </Button>
                  </CollapsibleTrigger>
                </div>
              </div>

              <CollapsibleContent className="pt-4">
                <div className="space-y-6">
                  <div className="overflow-x-auto">
                    <Table>
                      <TableHeader>
                        <TableRow>
                          <TableHead className="w-[200px] font-semibold">Type</TableHead>
                          <TableHead className="w-[300px] font-semibold">Description</TableHead>
                          <TableHead className="w-[120px] font-semibold">Activity Date</TableHead>
                          <TableHead className="w-[500px] font-semibold">Remarks</TableHead>
                        </TableRow>
                      </TableHeader>
                      <TableBody>
                        {activitiesLoading ? (
                          <TableRow>
                            <TableCell colSpan={4} className="text-center py-6 text-muted-foreground">
                              Loading activities...
                            </TableCell>
                          </TableRow>
                        ) : activitiesError ? (
                          <TableRow>
                            <TableCell colSpan={4} className="text-center py-6 text-destructive">
                              Error loading activities. Please try again.
                            </TableCell>
                          </TableRow>
                        ) : filteredActivities.length > 0 ? (
                          <>
                            {filteredActivities.map((activity, index) => (
                              <TableRow key={activity.id}>
                                <TableCell className="w-[200px] whitespace-nowrap">
                                  <div className="flex items-center">
                                    <span className="text-sm">{formatActivityType(activity.activity_type)}</span>
                                  </div>
                                </TableCell>
                                <TableCell className="text-sm break-words w-[300px]">
                                  <a 
                                    href="#" 
                                    className="text-blue-600 hover:underline"
                                    onClick={(e) => {
                                      e.preventDefault();
                                      handleViewDetails(activity);
                                    }}
                                  >
                                    {activity.description}
                                  </a>
                                </TableCell>
                                <TableCell className="w-[120px] whitespace-nowrap">
                                  {new Date(activity.created_at).toLocaleDateString('en-GB', {
                                    day: '2-digit',
                                    month: '2-digit',
                                    year: 'numeric'
                                  })}
                                </TableCell>
                                {index === 0 && (
                                  <TableCell rowSpan={filteredActivities.length} className="w-[500px] align-middle">
                                    {editingRemarks === (customerData?.id?.toString() || '') ? (
                                      <div className="flex items-center gap-2">
                                        <Textarea
                                          value={remarksEditForm.current}
                                          onChange={(e) => setRemarksEditForm({ ...remarksEditForm, current: e.target.value })}
                                          className="min-h-[100px] resize-y w-[400px]"
                                          placeholder="Enter remarks..."
                                        />
                                        <div className="flex flex-col gap-2">
                                          <Button
                                            variant="ghost"
                                            size="sm"
                                            onClick={() => handleSaveRemarks(customerData?.id?.toString() || '')}
                                          >
                                            <Check className="h-4 w-4" />
                                          </Button>
                                          <Button
                                            variant="ghost"
                                            size="sm"
                                            onClick={() => setEditingRemarks(null)}
                                          >
                                            <X className="h-4 w-4" />
                                          </Button>
                                        </div>
                                      </div>
                                    ) : (
                                      <div className="flex items-center gap-2">
                                        <span className="line-clamp-3 w-[400px]">
                                          {activity.reviewRemarksCurrent || "-"}
                                        </span>
                                        <div className="flex gap-1">
                                          <Button
                                            variant="ghost"
                                            size="sm"
                                            onClick={() => handleAddNewRemarks(customerData?.id?.toString() || '')}
                                          >
                                            <Plus className="h-4 w-4" />
                                          </Button>
                                          <Button
                                            variant="ghost"
                                            size="sm"
                                            onClick={() => handleEditRemarks(customerData?.id?.toString() || '', activity.reviewRemarksCurrent || '')}
                                          >
                                            <Edit className="h-4 w-4" />
                                          </Button>
                                          <Dialog>
                                            <DialogTrigger asChild>
                                              <Button variant="ghost" size="sm">
                                                <History className="h-4 w-4" />
                                              </Button>
                                            </DialogTrigger>
                                            <DialogContent className="max-w-[1000px]">
                                              <DialogHeader>
                                                <DialogTitle>Remarks History</DialogTitle>
                                              </DialogHeader>
                                              <div className="max-h-[400px] overflow-y-auto">
                                                <div className="space-y-4">
                                                  {activity.remarks_history?.length > 0 ? (
                                                    [...activity.remarks_history].reverse().map((remark, index) => (
                                                      <div key={index} className="border-b pb-3 last:border-b-0">
                                                        <div className="flex justify-between items-start mb-1">
                                                          <h4 className="text-sm font-medium">
                                                            Remark {index + 1}
                                                          </h4>
                                                          <span className="text-xs text-muted-foreground">
                                                            {new Date(remark.created_at).toLocaleDateString('en-GB', {
                                                              day: '2-digit',
                                                              month: '2-digit',
                                                              year: 'numeric'
                                                            })}
                                                          </span>
                                                        </div>
                                                        <p className="text-sm text-muted-foreground">
                                                          {remark.remarks || 'No remarks'}
                                                        </p>
                                                      </div>
                                                    ))
                                                  ) : (
                                                    <div className="text-sm text-muted-foreground">
                                                      No previous remarks
                                                    </div>
                                                  )}
                                                </div>
                                              </div>
                                            </DialogContent>
                                          </Dialog>
                                        </div>
                                      </div>
                                    )}
                                  </TableCell>
                                )}
                              </TableRow>
                            ))}
                          </>
                        ) : (
                          <TableRow>
                            <TableCell colSpan={3} className="text-center py-6 text-muted-foreground">
                              No activities found for this customer
                            </TableCell>
                            <TableCell className="w-[500px]">
                              {editingRemarks === (customerData?.id?.toString() || '') ? (
                                <div className="flex items-center gap-2">
                                  <Textarea
                                    value={remarksEditForm.current}
                                    onChange={(e) => setRemarksEditForm({ ...remarksEditForm, current: e.target.value })}
                                    className="min-h-[100px] resize-y w-[400px]"
                                    placeholder="Enter remarks..."
                                  />
                                  <div className="flex flex-col gap-2">
                                    <Button
                                      variant="ghost"
                                      size="sm"
                                      onClick={() => handleSaveRemarks(customerData?.id?.toString() || '')}
                                    >
                                      <Check className="h-4 w-4" />
                                    </Button>
                                    <Button
                                      variant="ghost"
                                      size="sm"
                                      onClick={() => setEditingRemarks(null)}
                                    >
                                      <X className="h-4 w-4" />
                                    </Button>
                                  </div>
                                </div>
                              ) : (
                                <div className="flex items-center gap-2">
                                  <span className="line-clamp-3 w-[400px]">
                                    {activities[0]?.reviewRemarksCurrent || "-"}
                                  </span>
                                  <div className="flex gap-1">
                                    <Button
                                      variant="ghost"
                                      size="sm"
                                      onClick={() => handleAddNewRemarks(customerData?.id?.toString() || '')}
                                    >
                                      <Plus className="h-4 w-4" />
                                    </Button>
                                    <Button
                                      variant="ghost"
                                      size="sm"
                                      onClick={() => handleEditRemarks(customerData?.id?.toString() || '', activities[0]?.reviewRemarksCurrent || '')}
                                    >
                                      <Edit className="h-4 w-4" />
                                    </Button>
                                    <Dialog>
                                      <DialogTrigger asChild>
                                        <Button variant="ghost" size="sm">
                                          <History className="h-4 w-4" />
                                        </Button>
                                      </DialogTrigger>
                                      <DialogContent className="max-w-[1000px]">
                                        <DialogHeader>
                                          <DialogTitle>Remarks History</DialogTitle>
                                        </DialogHeader>
                                        <div className="max-h-[400px] overflow-y-auto">
                                          <div className="space-y-4">
                                            {activities[0]?.remarks_history?.length > 0 ? (
                                              [...activities[0].remarks_history].reverse().map((remark, index) => (
                                                <div key={index} className="border-b pb-3 last:border-b-0">
                                                  <div className="flex justify-between items-start mb-1">
                                                    <h4 className="text-sm font-medium">
                                                      Remark {index + 1}
                                                    </h4>
                                                    <span className="text-xs text-muted-foreground">
                                                      {new Date(remark.created_at).toLocaleDateString('en-GB', {
                                                        day: '2-digit',
                                                        month: '2-digit',
                                                        year: 'numeric'
                                                      })}
                                                    </span>
                                                  </div>
                                                  <p className="text-sm text-muted-foreground">
                                                    {remark.remarks || 'No remarks'}
                                                  </p>
                                                </div>
                                              ))
                                            ) : (
                                              <div className="text-sm text-muted-foreground">
                                                No previous remarks
                                              </div>
                                            )}
                                          </div>
                                        </div>
                                      </DialogContent>
                                    </Dialog>
                                  </div>
                                </div>
                              )}
                            </TableCell>
                          </TableRow>
                        )}
                      </TableBody>
                    </Table>
                  </div>
                </div>
              </CollapsibleContent>
            </Collapsible>
          </div>
        </>
      )}

      <Dialog
        open={isAddChemicalDialogOpen}
        onOpenChange={setIsAddChemicalDialogOpen}
      >
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Add New Chemical</DialogTitle>
          </DialogHeader>
          <div className="py-4">
            <div className="relative">
              <Input
                ref={inputRef}
                placeholder="Enter chemical name"
                value={newChemicalName}
                onChange={(e) => handleChemicalNameChange(e.target.value)}
                onFocus={() => setShowSuggestions(true)}
                onKeyDown={(e) => {
                  if (e.key === "Enter") {
                    handleSubmitNewChemical();
                  }
                }}
              />
              {showSuggestions &&
                newChemicalName &&
                chemicalNameSuggestions.length > 0 && (
                  <div
                    ref={suggestionsRef}
                    className="absolute z-50 w-full mt-1 bg-white rounded-md shadow-lg border max-h-60 overflow-auto"
                  >
                    {filteredSuggestions.map((suggestion, index) => (
                      <div
                        key={index}
                        className="px-3 py-2 hover:bg-gray-100 cursor-pointer flex items-center"
                        onClick={() => {
                          handleChemicalSelect(suggestion);
                        }}
                      >
                        {suggestion}
                      </div>
                    ))}
                  </div>
                )}
            </div>
          </div>
          <DialogTrigger>
            <Button
              variant="outline"
              onClick={() => {
                setIsAddChemicalDialogOpen(false);
                setNewChemicalName("");
                setSelectedChemicalId(null);
                setShowSuggestions(false);
              }}
            >
              Cancel
            </Button>
            <Button onClick={handleSubmitNewChemical}>Add Chemical</Button>
          </DialogTrigger>
        </DialogContent>
      </Dialog>
    </div>
  );
};
