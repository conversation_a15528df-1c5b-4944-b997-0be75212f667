import { useState, useEffect } from "react";
import { Plus, X, Loader2 } from "lucide-react";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Customer, ContactEntry } from "./CustomerList";
import { COUNTRY_LIST } from "@/config/constants";
import { AutoComplete } from "antd";
import { useDebounce } from "@/hooks/useDebounce";
import axios from "axios";
import { API_CONFIG } from "@/config/api";
import { toast } from "sonner";

type AddCustomerFormProps = {
  onSubmit: (customer: Omit<Customer, 'id'>) => void;
  onCancel: () => void;
  initialData?: Customer;
  editMode?: "basic" | "contact";
};

// Add type for compound data
type Compound = {
  id: string;
  compound_id: string;
  chemical_name: string;
  // Add other fields if needed
};

export const AddCustomerForm = ({ onSubmit, onCancel, initialData, editMode }: AddCustomerFormProps) => {
  const [name, setName] = useState(initialData?.customer_full_name || "");
  const [city, setCity] = useState(initialData?.city || "");
  const [country, setCountry] = useState(initialData?.country || "");
  const [contacts, setContacts] = useState<ContactEntry[]>(
    initialData?.contacts && initialData.contacts.length > 0 
      ? initialData.contacts 
      : [{ name: "", role: "", email: "", phone: "" }]
  );
  const [industries, setIndustries] = useState<string[]>(
    initialData?.industries && initialData.industries.length > 0 
      ? initialData.industries 
      : [""]
  );
  const [expectedQuarterlyProcurement, setExpectedQuarterlyProcurement] = useState(
    initialData?.annual_chemicals_procurement || ""
  );

  
  const [chemicals, setChemicals] = useState<{ name: string; offset_chemical_id: string | null; offset_chemical_name: string | null; }[]>([]);
  const [annualRevenue, setAnnualRevenue] = useState(initialData?.annualRevenue || "0-25");

  // Add state for search term
  const [searchTerm, setSearchTerm] = useState('');

  // Add debounce hook
  const debouncedSearchTerm = useDebounce(searchTerm, 300);

  // Add state for chemical suggestions
  const [chemicalNameSuggestions, setChemicalNameSuggestions] = useState<string[]>([]);

  // Add state to store the full compound data
  const [chemicalNameToCompoundMap, setChemicalNameToCompoundMap] = useState<Map<string, Compound>>(new Map());

  // Effect to trigger search when debounced term changes
  useEffect(() => {
    searchChemicalSuggestions(debouncedSearchTerm);
  }, [debouncedSearchTerm]);

  // Add search function
  const searchChemicalSuggestions = async (searchTerm: string) => {
    if (!searchTerm || searchTerm.length < 2) {
      setChemicalNameSuggestions([]);
      return;
    }

    try {
      const response = await axios.get(
        `${API_CONFIG.catalogBaseUrl}catalog`,
        {
          params: { q: searchTerm },
          headers: {
            'accept': 'application/json',
            'Authorization': `Bearer ${API_CONFIG.accessToken}`
          }
        }
      );

      const compounds = response.data.data || [];
      const catalogResultsWithSynonyms = compounds.map(compound => ({
        displayName: compound.synonym ? `${compound.synonym} (${compound.chemical_name})` : compound.chemical_name,
        chemicalName: compound.chemical_name,
        compoundId: compound.compound_id,
        synonym: compound.synonym
      }));
      
      // Store the mapping of chemical names to their full compound data with proper typing
      const newMap = new Map<string, Compound>();
      compounds.forEach(compound => {
        // Map for chemical name
        newMap.set(compound.chemical_name, compound);
        // Map for synonym if it exists
        if (compound.synonym) {
          newMap.set(compound.synonym, compound);
        }
      });
      setChemicalNameToCompoundMap(newMap);
      
      // Set suggestions with synonyms
      setChemicalNameSuggestions(catalogResultsWithSynonyms.map(item => item.displayName));
    } catch (error) {
      console.error("Error searching chemical suggestions:", error);
      setChemicalNameSuggestions([]);
      setChemicalNameToCompoundMap(new Map());
    }
  };

  const addContact = () => {
    setContacts([...contacts, { name: "", role: "", email: "", phone: "" }]);
  };

  const removeContact = (index: number) => {
    setContacts(contacts.filter((_, i) => i !== index));
  };

  const updateContact = (index: number, field: keyof ContactEntry, value: string) => {
    const updatedContacts = [...contacts];
    updatedContacts[index] = { ...updatedContacts[index], [field]: value };
    setContacts(updatedContacts);
  };

  const addIndustry = () => {
    setIndustries([...industries, ""]);
  };

  const removeIndustry = (index: number) => {
    setIndustries(industries.filter((_, i) => i !== index));
  };

  const updateIndustry = (index: number, value: string) => {
    const updatedIndustries = [...industries];
    updatedIndustries[index] = value;
    setIndustries(updatedIndustries);
  };

  const addChemical = () => {
    setChemicals([...chemicals, { name: "", offset_chemical_id: null, offset_chemical_name: null }]);
  };

  const removeChemical = (index: number) => {
    setChemicals(chemicals.filter((_, i) => i !== index));
  };

  // Handle chemical name selection from suggestions
  const handleChemicalSelect = (value: string, index: number) => {
    // Extract the actual chemical name if it's a synonym (format: "synonym (chemical_name)")
    const match = value.match(/^(.*?)\s*\(([^()]+)\)$/);
    const actualChemicalName = match ? match[2] : value;
    
    const selectedCompound = chemicalNameToCompoundMap.get(actualChemicalName);
    
    if (!selectedCompound) {
      toast.error("Could not find matching chemical in catalog");
      return;
    }
    
    const updatedChemicals = [...chemicals];
    updatedChemicals[index] = { 
      ...updatedChemicals[index], 
      name: value, // Keep what user selected (including synonym if present)
      offset_chemical_id: selectedCompound.compound_id,
      offset_chemical_name: selectedCompound.chemical_name
    };
    setChemicals(updatedChemicals);
  };

  // Handle chemical name typing
  const updateChemical = (index: number, value: string) => {
    // Set the search term for suggestions
    setSearchTerm(value);

    // Only update the name
    const updatedChemicals = [...chemicals];
    updatedChemicals[index] = {
      ...updatedChemicals[index],
      name: value
    };
    setChemicals(updatedChemicals);
  };

  // Add effect to update offset details when map is updated
  useEffect(() => {
    chemicals.forEach((chemical, index) => {
      if (chemical.name) {
        const selectedCompound = chemicalNameToCompoundMap.get(chemical.name);
        if (selectedCompound) {
          const updatedChemicals = [...chemicals];
          updatedChemicals[index] = {
            ...updatedChemicals[index],
            offset_chemical_id: selectedCompound.compound_id,
            offset_chemical_name: selectedCompound.chemical_name
          };
          setChemicals(updatedChemicals);
        }
      }
    });
  }, [chemicalNameToCompoundMap]);

  const [loading, setLoading] = useState(false);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    try {
      const filteredContacts = contacts.filter(contact => contact.name.trim() !== "");
      const filteredIndustries = industries.filter(industry => industry.trim() !== "");
      const filteredChemicals = chemicals.filter(chemical => chemical.name.trim() !== "");

      console.log("filteredContacts", expectedQuarterlyProcurement);
      
      const updatedCustomer: Omit<Customer, 'id'> = {
        customer_full_name: name,
        city,
        country,
        contacts: filteredContacts,
        industries: filteredIndustries,
        expectedQuarterlyProcurement: expectedQuarterlyProcurement,
        annual_chemicals_procurement: expectedQuarterlyProcurement,
        projects: initialData?.projects || { enquiries: 0, samples: 0, pos: 0 },
        targetQuarterly: initialData?.targetQuarterly || "$0",
        revenueQuarterly: initialData?.revenueQuarterly || "$0",
        procurementMapped: initialData?.procurementMapped || 0,
        shareOfWallet: initialData?.shareOfWallet || 0,
        repeatRate: initialData?.repeatRate || 0,
        chemicals: filteredChemicals,
        annualRevenue,
        account_owner: initialData?.account_owner || "",
        activeEnquiries: initialData?.activeEnquiries || [],
        openSamples: initialData?.openSamples || [],
        openPOs: initialData?.openPOs || [],
        noActivity: initialData?.noActivity || filteredChemicals,
        industry: initialData?.industry || filteredIndustries[0] || "",
        website: initialData?.website || "",
        contactPerson: initialData?.contactPerson || (filteredContacts[0]?.name || ""),
        contactEmail: initialData?.contactEmail || (filteredContacts[0]?.email || ""),
        contactPhone: initialData?.contactPhone || (filteredContacts[0]?.phone || ""),
        turnover: initialData?.turnover || "$0",
        ytdOrderbook: initialData?.ytdOrderbook || "$0",
        ytdRevenue: initialData?.ytdRevenue || "$0",
        marginPercentage: initialData?.marginPercentage || 0,
        notes: initialData?.notes || "",
        targetQuarterlyPrev: initialData?.targetQuarterlyPrev || "$0",
        revenueQuarterlyPrev: initialData?.revenueQuarterlyPrev || "$0",
        procurementMappedPrev: initialData?.procurementMappedPrev || 0,
        shareOfWalletPrev: initialData?.shareOfWalletPrev || 0,
        repeatRatePrev: initialData?.repeatRatePrev || 0,
      };

      console.log("updatedCustomer", updatedCustomer);
      
      await onSubmit(updatedCustomer);
    } catch (error) {
      console.error("Error submitting form:", error);
    } finally {
      setLoading(false);
    }
  };

  if (editMode === "contact") {
    return (
      <form onSubmit={handleSubmit} className="space-y-4 bg-white rounded-md p-4 border">
        <div className="space-y-2">
          <div className="flex justify-between items-center">
            <Label className="font-semibold">Key Contact(s)</Label>
            <Button 
              type="button" 
              variant="ghost" 
              size="sm" 
              onClick={addContact}
              className="h-8 px-2 font-semibold"
            >
              <Plus size={16} className="mr-1" />
              Add Contact
            </Button>
          </div>
          
          {contacts.map((contact, index) => (
            <div key={index} className="flex items-start gap-2 pb-2 border-b">
              <div className="flex-1 grid grid-cols-1 md:grid-cols-4 gap-2">
                <div>
                  <Input 
                    placeholder="Name" 
                    value={contact.name} 
                    onChange={(e) => updateContact(index, "name", e.target.value)} 
                  />
                </div>
                <div>
                  <Input 
                    placeholder="Role" 
                    value={contact.role} 
                    onChange={(e) => updateContact(index, "role", e.target.value)} 
                  />
                </div>
                <div>
                  <Input 
                    placeholder="Email" 
                    type="email" 
                    value={contact.email} 
                    onChange={(e) => updateContact(index, "email", e.target.value)} 
                  />
                </div>
                <div>
                  <Input 
                    placeholder="Phone" 
                    value={contact.phone} 
                    onChange={(e) => updateContact(index, "phone", e.target.value)} 
                  />
                </div>
              </div>
              {contacts.length > 1 && (
                <Button 
                  type="button" 
                  variant="ghost" 
                  size="icon" 
                  onClick={() => removeContact(index)}
                  className="h-8 w-8 shrink-0"
                >
                  <X size={16} />
                </Button>
              )}
            </div>
          ))}
        </div>
        
        <div className="flex justify-end gap-3 pt-4">
          <Button type="button" variant="outline" onClick={onCancel} disabled={loading} className="font-semibold">
            Cancel
          </Button>
          <Button type="submit" disabled={loading}>
            {loading ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                {initialData ? "Updating..." : "Saving..."}
              </>
            ) : (
              initialData ? "Update Contacts" : "Add Contacts"
            )}
          </Button>
        </div>
      </form>
    );
  }

  if (editMode === "basic") {
    return (
      <form onSubmit={handleSubmit} className="space-y-4 bg-white rounded-md p-4 border">
        <div className="grid grid-cols-1 md:grid-cols-5 gap-4">
          <div className="space-y-2">
            <Label htmlFor="name" className="font-semibold">Customer Name *</Label>
            <Input 
              id="name" 
              value={name} 
              onChange={(e) => setName(e.target.value)} 
              required 
            />
          </div>
          
          <div className="space-y-2">
            <Label htmlFor="city" className="font-semibold">City *</Label>
            <Input 
              id="city" 
              value={city} 
              onChange={(e) => setCity(e.target.value)} 
              required 
            />
          </div>
          
          <div className="space-y-2">
            <Label htmlFor="country" className="font-semibold">Country *</Label>
            <Input 
              id="country" 
              value={country} 
              onChange={(e) => setCountry(e.target.value)} 
              placeholder="Enter country"
              required 
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="annualRevenue" className="font-semibold">Annual Turnover (Million $)</Label>
            <Select value={annualRevenue} onValueChange={setAnnualRevenue}>
              <SelectTrigger id="annualRevenue">
                <SelectValue placeholder="Select range" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="0-25">0-25</SelectItem>
                <SelectItem value="25-50">25-50</SelectItem>
                <SelectItem value="50-100">50-100</SelectItem>
                <SelectItem value="100-250">100-250</SelectItem>
                <SelectItem value="250-500">250-500</SelectItem>
                <SelectItem value=">500">&gt;500</SelectItem>
              </SelectContent>
            </Select>
          </div>
          
          <div className="space-y-2">
            <Label htmlFor="expectedProcurement" className="font-semibold">Annual Chemicals Procurement ($)</Label>
            <Input 
              id="expectedProcurement" 
              value={expectedQuarterlyProcurement} 
              onChange={(e) => {
                setExpectedQuarterlyProcurement(e.target.value)}} 
              placeholder="e.g. 45,000"
            />
          </div>
        </div>
        
        <div className="grid grid-cols-2 gap-4">
          <div className="space-y-2">
            <div className="flex justify-between items-center">
              <Label className="font-semibold">Industries</Label>
              <Button 
                type="button" 
                variant="ghost" 
                size="sm" 
                onClick={addIndustry}
                className="h-8 px-2 font-semibold"
              >
                <Plus size={16} className="mr-1" />
                Add Industry
              </Button>
            </div>
            
            <div className="flex flex-wrap gap-2">
              {industries.map((industry, index) => (
                <div key={index} className="flex items-center">
                  <Input 
                    placeholder="Industry" 
                    value={industry} 
                    onChange={(e) => updateIndustry(index, e.target.value)} 
                    className="w-40"
                  />
                  {industries.length > 1 && (
                    <Button 
                      type="button" 
                      variant="ghost" 
                      size="icon" 
                      onClick={() => removeIndustry(index)}
                      className="h-8 w-8"
                    >
                      <X size={16} />
                    </Button>
                  )}
                </div>
              ))}
            </div>
          </div>
          
          {/* <div className="space-y-2">
            <div className="flex justify-between items-center">
              <Label>Chemicals</Label>
              <Button 
                type="button" 
                variant="ghost" 
                size="sm" 
                onClick={addChemical}
                className="h-8 px-2"
              >
                <Plus size={16} className="mr-1" />
                Add Chemical
              </Button>
            </div>
            
            <div className="flex flex-wrap gap-2">
              {chemicals.map((chemical, index) => (
                <div key={index} className="flex items-center">
                  <div className="w-40">
                    <AutoComplete
                      placeholder="Chemical" 
                      value={chemical.name} 
                      onChange={(value) => updateChemical(index, value)}
                      options={chemicalNameSuggestions.map(name => ({ value: name }))}
                      filterOption={(inputValue, option) =>
                        option!.value.toString().toLowerCase().includes(inputValue.toLowerCase())
                      }
                      style={{
                        width: "100%",
                        height: "40px",
                        border: "none",
                        boxShadow: "none",
                      }}
                    />
                  </div>
                  {chemicals.length > 1 && (
                    <Button 
                      type="button" 
                      variant="ghost" 
                      size="icon" 
                      onClick={() => removeChemical(index)}
                      className="h-8 w-8"
                    >
                      <X size={16} />
                    </Button>
                  )}
                </div>
              ))}
            </div>
          </div> */}
        </div>
        
        <div className="flex justify-end gap-3 pt-4">
          <Button type="button" variant="outline" onClick={onCancel} disabled={loading} className="font-semibold">
            Cancel
          </Button>
          <Button type="submit" disabled={loading}>
            {loading ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Updating...
              </>
            ) : (
              initialData ? "Update Customer" : "Add Customer"
            )}
          </Button>
        </div>
      </form>
    );
  }

  return (
    <form onSubmit={handleSubmit} className="space-y-4 bg-white rounded-md p-4 border">
      <div className="grid grid-cols-1 md:grid-cols-5 gap-4">
        <div className="space-y-2">
          <Label htmlFor="name" className="font-semibold">Customer Name *</Label>
          <Input 
            id="name" 
            value={name} 
            onChange={(e) => setName(e.target.value)} 
            required 
          />
        </div>
        
        <div className="space-y-2">
          <Label htmlFor="city" className="font-semibold">City *</Label>
          <Input 
            id="city" 
            value={city} 
            onChange={(e) => setCity(e.target.value)} 
            required 
          />
        </div>
        
        <div className="space-y-2">
          <Label htmlFor="country" className="font-semibold">Country *</Label>
          <Input 
            id="country" 
            value={country} 
            onChange={(e) => setCountry(e.target.value)} 
            placeholder="Enter country"
            required 
          />
        </div>

        <div className="space-y-2">
          <Label htmlFor="annualRevenue" className="font-semibold">Annual Turnover (Million $)</Label>
          <Select value={annualRevenue} onValueChange={setAnnualRevenue}>
            <SelectTrigger id="annualRevenue">
              <SelectValue placeholder="Select range" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="0-25">0-25</SelectItem>
              <SelectItem value="25-50">25-50</SelectItem>
              <SelectItem value="50-100">50-100</SelectItem>
              <SelectItem value="100-250">100-250</SelectItem>
              <SelectItem value="250-500">250-500</SelectItem>
              <SelectItem value=">500">&gt;500</SelectItem>
            </SelectContent>
          </Select>
        </div>
        
        <div className="space-y-2">
          <Label htmlFor="expectedProcurement" className="font-semibold">Annual Chemicals Procurement ($)</Label>
          <Input 
            id="expectedProcurement" 
            value={expectedQuarterlyProcurement} 
            onChange={(e) => setExpectedQuarterlyProcurement(e.target.value)} 
            placeholder="e.g. 45,000"
          />
        </div>
      </div>
      
      <div className="space-y-2">
        <div className="flex justify-between items-center">
          <Label className="font-semibold">Key Contact(s)</Label>
          <Button 
            type="button" 
            variant="ghost" 
            size="sm" 
            onClick={addContact}
            className="h-8 px-2 font-semibold"
          >
            <Plus size={16} className="mr-1" />
            Add Contact
          </Button>
        </div>
        
        {contacts.map((contact, index) => (
          <div key={index} className="flex items-start gap-2 pb-2 border-b">
            <div className="flex-1 grid grid-cols-1 md:grid-cols-4 gap-2">
              <div>
                <Input 
                  placeholder="Name" 
                  value={contact.name} 
                  onChange={(e) => updateContact(index, "name", e.target.value)} 
                />
              </div>
              <div>
                <Input 
                  placeholder="Role" 
                  value={contact.role} 
                  onChange={(e) => updateContact(index, "role", e.target.value)} 
                />
              </div>
              <div>
                <Input 
                  placeholder="Email" 
                  type="email" 
                  value={contact.email} 
                  onChange={(e) => updateContact(index, "email", e.target.value)} 
                />
              </div>
              <div>
                <Input 
                  placeholder="Phone" 
                  value={contact.phone} 
                  onChange={(e) => updateContact(index, "phone", e.target.value)} 
                />
              </div>
            </div>
            {contacts.length > 1 && (
              <Button 
                type="button" 
                variant="ghost" 
                size="icon" 
                onClick={() => removeContact(index)}
                className="h-8 w-8 shrink-0"
              >
                <X size={16} />
              </Button>
            )}
          </div>
        ))}
      </div>
      
      <div className="grid grid-cols-2 gap-4">
        <div className="space-y-2">
          <div className="flex justify-between items-center">
            <Label className="font-semibold">Industries</Label>
            <Button 
              type="button" 
              variant="ghost" 
              size="sm" 
              onClick={addIndustry}
              className="h-8 px-2 font-semibold"
            >
              <Plus size={16} className="mr-1" />
              Add Industry
            </Button>
          </div>
          
          <div className="flex flex-wrap gap-2">
            {industries.map((industry, index) => (
              <div key={index} className="flex items-center">
                <Input 
                  placeholder="Industry" 
                  value={industry} 
                  onChange={(e) => updateIndustry(index, e.target.value)} 
                  className="w-40"
                />
                {industries.length > 1 && (
                  <Button 
                    type="button" 
                    variant="ghost" 
                    size="icon" 
                    onClick={() => removeIndustry(index)}
                    className="h-8 w-8"
                  >
                    <X size={16} />
                  </Button>
                )}
              </div>
            ))}
          </div>
        </div>
        
        <div className="space-y-2">
          <div className="flex justify-between items-center">
            <Label className="font-semibold">Chemicals</Label>
            <Button 
              type="button" 
              variant="ghost" 
              size="sm" 
              onClick={addChemical}
              className="h-8 px-2 font-semibold"
            >
              <Plus size={16} className="mr-1" />
              Add Chemical
            </Button>
          </div>
          
          <div className="flex flex-wrap gap-2">
            {chemicals.map((chemical, index) => (
              <div key={index} className="flex items-center">
                <div className="w-40">
                  <AutoComplete
                    placeholder="Chemical" 
                    value={chemical.name || ""}
                    onChange={(value) => updateChemical(index, value)}
                    onSelect={(value) => handleChemicalSelect(value, index)}
                    options={chemicalNameSuggestions.map(name => ({ value: name }))}
                    filterOption={(inputValue: string, option: any) => {
                      if (!option?.value) return false;
                      const searchTerm = inputValue.toLowerCase();
                      const optionValue = option.value.toLowerCase();
                      
                      // Exact match
                      if (optionValue.includes(searchTerm)) return true;
                      
                      // Fuzzy match - check if all characters in search term appear in order
                      const searchChars = searchTerm.split('');
                      let lastIndex = -1;
                      return searchChars.every(char => {
                        const index = optionValue.indexOf(char, lastIndex + 1);
                        if (index === -1) return false;
                        lastIndex = index;
                        return true;
                      });
                    }}
                    style={{
                      width: "100%",
                      height: "40px",
                      border: "none",
                      boxShadow: "none",
                    }}
                  />
                </div>
                {chemicals.length > 1 && (
                  <Button 
                    type="button" 
                    variant="ghost" 
                    size="icon" 
                    onClick={() => removeChemical(index)}
                    className="h-8 w-8"
                  >
                    <X size={16} />
                  </Button>
                )}
              </div>
            ))}
          </div>
        </div>
      </div>
      
      <div className="flex justify-end gap-3 pt-4">
        <Button type="button" variant="outline" onClick={onCancel} disabled={loading} className="font-semibold">
          Cancel
        </Button>
        <Button type="submit" disabled={loading}>
          {loading ? (
            <>
              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              Updating...
            </>
          ) : (
            initialData ? "Update Customer" : "Add Customer"
          )}
        </Button>
      </div>
    </form>
  );
};
