import { useState, useEffect } from "react";
import { Card, CardContent } from "@/components/ui/card";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Label } from "@/components/ui/label";
import { formatEmailToName } from '@/utils/format';

type Customer = {
  id: string;
  customer_full_name: string;
  account_owner: string;
};

type MeetingsFilterProps = {
  onFilter: (params: FilterParams) => void;
  customers: Customer[];
  accountOwnerList: string[];
  initialFilters?: FilterParams;
};

type FilterParams = {
  customerName: string;
  accountOwner: string;
  dateFilter: string;
  meetingType: string;
};

export const MeetingsFilter = ({ 
  onFilter, 
  customers, 
  accountOwnerList,
  initialFilters 
}: MeetingsFilterProps) => {
  const [customerName, setCustomerName] = useState(initialFilters?.customerName || "");
  const [accountOwner, setAccountOwner] = useState(initialFilters?.accountOwner || "");
  const [dateFilter, setDateFilter] = useState(initialFilters?.dateFilter || "all");
  const [meetingType, setMeetingType] = useState(initialFilters?.meetingType || "all");
  const [isAdmin, setIsAdmin] = useState(false);
  const [currentUser, setCurrentUser] = useState<string>("");
  
  // Filter customers based on selected account owner
  const filteredCustomers = accountOwner === "all" || !accountOwner
    ? customers
    : customers.filter(c => c.account_owner === accountOwner);

  useEffect(() => {
    async function fetchUserRole() {
      const user = {
        email: localStorage.getItem('userEmail'),
        id: localStorage.getItem('userId'),
        role: localStorage.getItem('userRole'),
        country: localStorage.getItem('userCountry'),
        category: localStorage.getItem('userCategory')
      }
      if (user?.email) {
        setCurrentUser(user.email);
        setIsAdmin(user?.role === 'admin' || user?.role === 'bu_head');
        
        // If sales, set selected owner to current user
        if (user?.role === 'sales') {
          setAccountOwner(user.email);
          handleFilterChange("accountOwner", user.email);
        }
      }
    }
    fetchUserRole();
  }, []);

  useEffect(() => {
    if (initialFilters) {
      setCustomerName(initialFilters.customerName || "");
      setAccountOwner(initialFilters.accountOwner || "");
      setDateFilter(initialFilters.dateFilter || "all");
      setMeetingType(initialFilters.meetingType || "all");
    }
  }, [initialFilters]);
  
  const handleFilterChange = (type: keyof FilterParams, value: string) => {
    if (type === "customerName") {
      setCustomerName(value);
    } else if (type === "accountOwner") {
      setAccountOwner(value);
      // Reset customer to "all" when account owner changes
      if (customerName !== "all" && customerName !== "") {
        setCustomerName("all");
        onFilter({
          customerName: "all",
          accountOwner: value,
          dateFilter: dateFilter,
          meetingType: meetingType
        });
        return;
      }
    } else if (type === "dateFilter") {
      setDateFilter(value);
    } else if (type === "meetingType") {
      setMeetingType(value);
    }
    
    onFilter({
      customerName: type === "customerName" ? value : customerName,
      accountOwner: type === "accountOwner" ? value : accountOwner,
      dateFilter: type === "dateFilter" ? value : dateFilter,
      meetingType: type === "meetingType" ? value : meetingType
    });
  };
  
  return (
    <Card className="mb-6">
      <CardContent className="pt-6">
        <div className="flex flex-col md:flex-row gap-4 md:items-end">
          <div className="flex-1">
            <div className="space-y-2">
              <Label htmlFor="account-owner-filter">Account Owner</Label>
              <Select
                value={accountOwner}
                onValueChange={(value) => handleFilterChange("accountOwner", value)}
                disabled={!isAdmin}
              >
                <SelectTrigger id="account-owner-filter">
                  <SelectValue placeholder="All account owners" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All account owners</SelectItem>
                  {accountOwnerList.map((owner, index) => (
                    <SelectItem key={index} value={owner}>
                      {formatEmailToName(owner)}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>
          
          <div className="flex-1">
            <div className="space-y-2">
              <Label htmlFor="customer-filter">Customer</Label>
              <Select
                value={customerName}
                onValueChange={(value) => handleFilterChange("customerName", value)}
              >
                <SelectTrigger id="customer-filter">
                  <SelectValue placeholder="All customers" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All customers</SelectItem>
                  {filteredCustomers.map((customer) => (
                    <SelectItem key={customer.id} value={customer.customer_full_name}>
                      {customer.customer_full_name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>
          
          <div className="flex-1">
            <div className="space-y-2">
              <Label htmlFor="meeting-type-filter">Meeting Type</Label>
              <Select
                value={meetingType}
                onValueChange={(value) => handleFilterChange("meetingType", value)}
              >
                <SelectTrigger id="meeting-type-filter">
                  <SelectValue placeholder="All meeting types" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All meeting types</SelectItem>
                  <SelectItem value="Phone Call">Phone Call</SelectItem>
                  <SelectItem value="Virtual Meeting">Virtual Meeting</SelectItem>
                  <SelectItem value="In-person Meeting">In-person Meeting</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
          
          <div className="flex-1">
            <div className="space-y-2">
              <Label htmlFor="date-filter">Date</Label>
              <Select
                value={dateFilter}
                onValueChange={(value) => handleFilterChange("dateFilter", value)}
              >
                <SelectTrigger id="date-filter">
                  <SelectValue placeholder="All dates" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All dates</SelectItem>
                  <SelectItem value="today">Today</SelectItem>
                  <SelectItem value="thisWeek">This week</SelectItem>
                  <SelectItem value="thisMonth">This month</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};
