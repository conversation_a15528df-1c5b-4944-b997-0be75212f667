import React, { useEffect, useState } from "react";
// import LandingPageImage from "@/assets/LandingPageImage.png";
import LandingPageIcon from "@/assets/LandingPageIcon.svg"
import { SidebarLayout } from "@/components/layout/SidebarLayout";
import { useLocation, useNavigate } from "react-router-dom";
import { supabase } from "@/integrations/supabase/client";
import { toast } from "sonner";
import { setCrmEnabledEverywhere } from "@/lib/utils";

// Card data array
const CARD_DATA = [
  {
    key: "enquiries",
    icon: (
      <svg
        xmlns="http://www.w3.org/2000/svg"
        width="32"
        height="34"
        viewBox="0 0 32 34"
        fill="none"
      >
        <path
          d="M31 21C31 21.8841 30.6488 22.7319 30.0237 23.357C29.3986 23.9821 28.5507 24.3333 27.6667 24.3333H7.66667L1 31V4.33333C1 3.44928 1.35119 2.60143 1.97631 1.97631C2.60143 1.35119 3.44928 1 4.33333 1H27.6667C28.5507 1 29.3986 1.35119 30.0237 1.97631C30.6488 2.60143 31 3.44928 31 4.33333V21Z"
          stroke="#2A4D48"
          strokeWidth="2"
        />
      </svg>
    ),
    title: "Enquiries",
    description: "Create and revise customer enquiries",
    tab: "salesstack",
  },
  {
    key: "customers",
    icon: (
      <svg
        xmlns="http://www.w3.org/2000/svg"
        width="50"
        height="35"
        viewBox="0 0 50 35"
        fill="none"
      >
        <g clip-path="url(#clip0_1_266)">
          <path
            d="M13.75 23.5C11.5811 23.5 8.26992 24.0518 5.52637 25.1504C4.15548 25.6994 2.98917 26.3602 2.18164 27.1055C1.38094 27.8445 1 28.5984 1 29.375V34H26.5V29.375C26.5 28.5984 26.1191 27.8445 25.3184 27.1055C24.5108 26.3602 23.3445 25.6994 21.9736 25.1504C19.2301 24.0518 15.9189 23.5 13.75 23.5ZM32.5 21C31.5561 21 30.4319 21.0849 29.2188 21.252C32.7816 24.0474 33.5 27.302 33.5 29.375V34H49V28.75C49 27.6608 48.4627 26.6335 47.4082 25.6602C46.3468 24.6805 44.8289 23.8244 43.0674 23.1191C39.5426 21.708 35.294 21 32.5 21ZM13.75 6C10.8575 6 8.52539 8.34705 8.52539 11.25C8.52539 14.1529 10.8575 16.5 13.75 16.5C16.6477 16.5 19 14.1477 19 11.25C19 8.35228 16.6477 6 13.75 6ZM32.5 1C28.9191 1 26.0254 3.91043 26.0254 7.5C26.0254 11.0896 28.9191 14 32.5 14C36.0852 14 39 11.0852 39 7.5C39 3.91478 36.0852 1 32.5 1Z"
            stroke="#2A4D48"
            stroke-width="2"
          />
        </g>
        <defs>
          <clipPath id="clip0_1_266">
            <rect
              width="50"
              height="35"
              fill="white"
              transform="matrix(-1 0 0 1 50 0)"
            />
          </clipPath>
        </defs>
      </svg>
    ),
    title: "Customers",
    description: "Access your customer database",
    tab: "customers",
  },
  {
    key: "meeting-notes",
    icon: (
      <svg
        xmlns="http://www.w3.org/2000/svg"
        width="40"
        height="40"
        viewBox="0 0 40 40"
        fill="none"
      >
        <path
          d="M6.87846 37.8125H26.9916C27.1515 37.8079 27.3038 37.7434 27.4184 37.6317L37.0328 28.6273C37.0938 28.5669 37.1417 28.4948 37.1737 28.4152C37.2057 28.3355 37.2211 28.2502 37.2188 28.1645V8.64548C37.2204 8.13563 37.1213 7.63047 36.927 7.15908C36.7327 6.6877 36.4471 6.25938 36.0866 5.89879C35.7262 5.5382 35.2979 5.25245 34.8266 5.05798C34.3553 4.8635 33.8502 4.76415 33.3403 4.76564H30.4219V2.83728C30.4219 2.67152 30.356 2.51255 30.2388 2.39534C30.1216 2.27813 29.9627 2.21228 29.7969 2.21228C29.6311 2.21228 29.4722 2.27813 29.355 2.39534C29.2377 2.51255 29.1719 2.67152 29.1719 2.83728V4.76564H11.0469V2.83728C11.0469 2.67152 10.981 2.51255 10.8638 2.39534C10.7466 2.27813 10.5877 2.21228 10.4219 2.21228C10.2561 2.21228 10.0972 2.27813 9.97995 2.39534C9.86274 2.51255 9.7969 2.67152 9.7969 2.83728V4.76564H6.87846C6.3686 4.76415 5.86348 4.8635 5.39216 5.05798C4.92085 5.25245 4.49264 5.5382 4.13218 5.89879C3.77172 6.25938 3.48612 6.6877 3.29182 7.15908C3.09752 7.63047 2.99835 8.13563 3.00002 8.64548V33.8849C3.00002 36.0369 4.72658 37.8125 6.87846 37.8125ZM4.25002 33.8849V14.9219H35.9688V27.5781H29.2962C28.4783 27.5774 27.6935 27.9009 27.1137 28.4778C26.5339 29.0547 26.2065 29.8378 26.2031 30.6557V36.5625H6.87846C5.41565 36.5625 4.25002 35.3477 4.25002 33.8849ZM34.9991 28.8281L27.4531 35.8667V30.6558C27.4561 30.1693 27.6517 29.7038 27.9971 29.3613C28.3426 29.0187 28.8097 28.827 29.2962 28.8281H34.9991ZM6.87846 6.01564H9.7969V8.63962C9.7969 8.80538 9.86274 8.96435 9.97995 9.08157C10.0972 9.19878 10.2561 9.26462 10.4219 9.26462C10.5877 9.26462 10.7466 9.19878 10.8638 9.08157C10.981 8.96435 11.0469 8.80538 11.0469 8.63962V6.01564H29.1719V8.63962C29.1719 8.80538 29.2377 8.96435 29.355 9.08157C29.4722 9.19878 29.6311 9.26462 29.7969 9.26462C29.9627 9.26462 30.1216 9.19878 30.2388 9.08157C30.356 8.96435 30.4219 8.80538 30.4219 8.63962V6.01564H33.3403C33.6861 6.01392 34.0288 6.08078 34.3486 6.21235C34.6683 6.34392 34.9588 6.53759 35.2033 6.78215C35.4477 7.02672 35.6412 7.31733 35.7726 7.63716C35.904 7.957 35.9707 8.29971 35.9688 8.64548V13.6719H4.25002V8.64548C4.24812 8.29971 4.31479 7.957 4.44619 7.63716C4.57759 7.31733 4.7711 7.02672 5.01554 6.78215C5.25997 6.53759 5.55048 6.34392 5.87024 6.21235C6.19001 6.08078 6.53269 6.01392 6.87846 6.01564Z"
          fill="#324C48"
          stroke="#324C48"
        />
      </svg>
    ),
    title: "Meetings",
    tab: "meetings",
    description: "Update and manage meeting records",
  },
  {
    key: "business-view",
    icon: (
      <svg
        xmlns="http://www.w3.org/2000/svg"
        width="39"
        height="38"
        viewBox="0 0 39 38"
        fill="none"
      >
        <path
          fill-rule="evenodd"
          clip-rule="evenodd"
          d="M3.10438 -0.75C2.76875 -0.75 2.5 -0.46125 2.5 -0.103125V33.625H1.27188C0.91375 33.625 0.625 33.8944 0.625 34.2294V34.2706C0.625 34.6056 0.91375 34.875 1.27188 34.875H2.5V36.1031C2.5 36.4613 2.76938 36.75 3.10438 36.75H3.14563C3.48125 36.75 3.75 36.4613 3.75 36.1031V34.875H37.4781C37.8363 34.875 38.125 34.6056 38.125 34.2706V34.2294C38.125 33.8944 37.8363 33.625 37.4781 33.625H3.75V-0.103125C3.75 -0.46125 3.48063 -0.75 3.14563 -0.75H3.10438ZM37.4244 7.98125C37.3618 7.98437 37.2994 7.99063 37.2375 8H32.5L34.79 10.29L30.2575 16.0588L27.34 13.9756C26.9449 13.6932 26.4556 13.5752 25.9752 13.6463C25.4948 13.7174 25.0607 13.9721 24.7644 14.3569L19.5825 21.0938L15.415 18.3156C15.0246 18.0556 14.5509 17.9513 14.0874 18.0234C13.6239 18.0955 13.2042 18.3387 12.9113 18.705L4.375 29.3744V33H6.27563L14.7656 22.3894L18.9594 25.185C19.3548 25.4487 19.8356 25.5525 20.3046 25.4752C20.7736 25.3979 21.1957 25.1454 21.4856 24.7688L26.6331 18.0781L29.535 20.1506C29.9275 20.4312 30.4131 20.5496 30.8907 20.4812C31.3683 20.4129 31.8012 20.1631 32.0994 19.7838L37.4606 12.9613L39.375 14.875V10.0875C39.3949 9.93063 39.3949 9.77187 39.375 9.615V8H37.7856C37.6661 7.98212 37.5451 7.97584 37.4244 7.98125Z"
          fill="#2A4D48"
        />
      </svg>
    ),
    title: "Current Snapshot",
    description: "View real-time business overview",
    tab: "snapshot",
  },
  {
    key: "dashboard",
    icon: (
      <svg
        xmlns="http://www.w3.org/2000/svg"
        width="40"
        height="40"
        viewBox="0 0 40 40"
        fill="none"
      >
        <path
          d="M18.8235 4.62706H5.2153C5.05929 4.62706 4.90967 4.68904 4.79935 4.79935C4.68904 4.90967 4.62706 5.05929 4.62706 5.2153V14.6718C4.62706 14.8278 4.68904 14.9774 4.79935 15.0877C4.90967 15.198 5.05929 15.26 5.2153 15.26H18.8235C18.9795 15.26 19.1292 15.198 19.2395 15.0877C19.3498 14.9774 19.4118 14.8278 19.4118 14.6718V5.2153C19.4118 5.05929 19.3498 4.90967 19.2395 4.79935C19.1292 4.68904 18.9795 4.62706 18.8235 4.62706ZM18.2353 14.0835H5.80353V5.80353H18.2353V14.0835ZM18.8235 16.3918H5.2153C5.05929 16.3918 4.90967 16.4537 4.79935 16.5641C4.68904 16.6744 4.62706 16.824 4.62706 16.98V34.6718C4.62706 34.8278 4.68904 34.9774 4.79935 35.0877C4.90967 35.198 5.05929 35.26 5.2153 35.26H18.8235C18.9795 35.26 19.1292 35.198 19.2395 35.0877C19.3498 34.9774 19.4118 34.8278 19.4118 34.6718V16.98C19.4118 16.824 19.3498 16.6744 19.2395 16.5641C19.1292 16.4537 18.9795 16.3918 18.8235 16.3918ZM18.2353 34.0835H5.80353V17.5682H18.2353V34.0835ZM34.7847 21.1424H21.1765C21.0205 21.1424 20.8708 21.2043 20.7605 21.3146C20.6502 21.425 20.5882 21.5746 20.5882 21.7306V34.6718C20.5882 34.8278 20.6502 34.9774 20.7605 35.0877C20.8708 35.198 21.0205 35.26 21.1765 35.26H34.7847C34.9407 35.26 35.0903 35.198 35.2007 35.0877C35.311 34.9774 35.3729 34.8278 35.3729 34.6718V21.7306C35.3729 21.5746 35.311 21.425 35.2007 21.3146C35.0903 21.2043 34.9407 21.1424 34.7847 21.1424ZM34.1965 34.0835H21.7647V22.3188H34.1965V34.0835ZM33.8141 8.0953C33.8141 8.08941 33.8024 8.08706 33.7988 8.08118C32.9292 6.96603 31.7579 6.12376 30.4239 5.65429C29.0899 5.18482 27.6493 5.10786 26.2729 5.43254C24.8965 5.75723 23.6422 6.46992 22.6586 7.48607C21.6751 8.50222 21.0037 9.77918 20.7242 11.1654C20.4446 12.5517 20.5685 13.989 21.0812 15.307C21.594 16.6249 22.474 17.7681 23.617 18.6009C24.7599 19.4337 26.1178 19.9212 27.5294 20.0055C28.9411 20.0898 30.3473 19.7673 31.5812 19.0765H31.5918L31.6071 19.0635C32.5317 18.5455 33.3336 17.834 33.958 16.9777C34.5824 16.1213 35.0147 15.1403 35.2252 14.1016C35.4357 13.0628 35.4196 11.9909 35.1779 10.959C34.9362 9.92711 34.4746 8.95953 33.8247 8.12236C33.82 8.11059 33.8188 8.10236 33.8141 8.0953ZM32.4918 8.37647L28.5682 11.4247V6.47059C30.0644 6.60724 31.4594 7.28488 32.4918 8.37647ZM21.7647 12.6259C21.7698 11.084 22.3489 9.59932 23.3893 8.46131C24.4296 7.32331 25.8565 6.6136 27.3918 6.47059V12.6259C27.3918 12.6353 27.3988 12.6424 27.3988 12.6506C27.4003 12.7426 27.4236 12.833 27.4671 12.9141L30.4871 18.3047C29.6984 18.6579 28.8442 18.841 27.98 18.8424C26.332 18.8405 24.752 18.1849 23.5868 17.0195C22.4216 15.854 21.7663 14.2739 21.7647 12.6259ZM31.5129 17.7306L28.7412 12.7812L33.2118 9.30471C34.0806 10.6544 34.3889 12.2898 34.071 13.8632C33.7531 15.4366 32.8341 16.824 31.5094 17.7306H31.5129ZM25.5941 24.0835C25.5941 23.9275 25.6561 23.7779 25.7664 23.6676C25.8767 23.5573 26.0263 23.4953 26.1824 23.4953H32.4318C32.5878 23.4953 32.7374 23.5573 32.8477 23.6676C32.958 23.7779 33.02 23.9275 33.02 24.0835C33.02 24.2395 32.958 24.3892 32.8477 24.4995C32.7374 24.6098 32.5878 24.6718 32.4318 24.6718H26.1824C26.0263 24.6718 25.8767 24.6098 25.7664 24.4995C25.6561 24.3892 25.5941 24.2395 25.5941 24.0835ZM33.02 26.4365C33.02 26.5925 32.958 26.7421 32.8477 26.8524C32.7374 26.9627 32.5878 27.0247 32.4318 27.0247H28.5353C28.3793 27.0247 28.2297 26.9627 28.1194 26.8524C28.009 26.7421 27.9471 26.5925 27.9471 26.4365C27.9471 26.2805 28.009 26.1308 28.1194 26.0205C28.2297 25.9102 28.3793 25.8482 28.5353 25.8482H32.4318C32.509 25.8482 32.5855 25.8635 32.6569 25.893C32.7282 25.9226 32.7931 25.9659 32.8477 26.0205C32.9023 26.0752 32.9457 26.14 32.9752 26.2114C33.0048 26.2827 33.02 26.3592 33.02 26.4365ZM8.74942 19.3777C8.74942 19.2216 8.81139 19.072 8.92171 18.9617C9.03202 18.8514 9.18164 18.7894 9.33765 18.7894H14.3271C14.4831 18.7894 14.6327 18.8514 14.743 18.9617C14.8533 19.072 14.9153 19.2216 14.9153 19.3777C14.9153 19.5337 14.8533 19.6833 14.743 19.7936C14.6327 19.9039 14.4831 19.9659 14.3271 19.9659H9.33765C9.18164 19.9659 9.03202 19.9039 8.92171 19.7936C8.81139 19.6833 8.74942 19.5337 8.74942 19.3777ZM7.02353 19.6C6.99551 19.5286 6.9796 19.4531 6.97647 19.3765C6.97981 19.2216 7.04306 19.074 7.15294 18.9647C7.26445 18.8554 7.41441 18.7941 7.57059 18.7941C7.72678 18.7941 7.87673 18.8554 7.98824 18.9647C8.0941 19.0757 8.15317 19.2231 8.15317 19.3765C8.15317 19.5298 8.0941 19.6773 7.98824 19.7882C7.93428 19.8484 7.86523 19.8931 7.78824 19.9177C7.71863 19.9512 7.64193 19.9673 7.56471 19.9647C7.48755 19.9657 7.41104 19.9505 7.34011 19.9201C7.26919 19.8897 7.20542 19.8448 7.15294 19.7882C7.09682 19.7355 7.05269 19.6713 7.02353 19.6ZM8.74942 21.7306C8.74942 21.5746 8.81139 21.425 8.92171 21.3146C9.03202 21.2043 9.18164 21.1424 9.33765 21.1424H14.3271C14.4831 21.1424 14.6327 21.2043 14.743 21.3146C14.8533 21.425 14.9153 21.5746 14.9153 21.7306C14.9153 21.8866 14.8533 22.0362 14.743 22.1465C14.6327 22.2569 14.4831 22.3188 14.3271 22.3188H9.33765C9.18164 22.3188 9.03202 22.2569 8.92171 22.1465C8.81139 22.0362 8.74942 21.8866 8.74942 21.7306ZM7.15294 22.1529C7.04473 22.0378 6.98202 21.8873 6.97647 21.7294C6.9796 21.6528 6.99551 21.5773 7.02353 21.5059C7.05269 21.4346 7.09682 21.3704 7.15294 21.3177C7.23451 21.2362 7.33821 21.1805 7.45115 21.1575C7.5641 21.1345 7.68131 21.1452 7.78824 21.1882C7.86523 21.2128 7.93428 21.2575 7.98824 21.3177C8.09476 21.4282 8.15387 21.5759 8.15295 21.7294C8.15627 21.8076 8.14334 21.8855 8.11495 21.9584C8.08657 22.0313 8.04336 22.0975 7.98806 22.1528C7.93276 22.2081 7.86657 22.2513 7.7937 22.2797C7.72082 22.308 7.64285 22.321 7.56471 22.3176C7.41122 22.3186 7.26345 22.2595 7.15294 22.1529ZM8.74942 24.0835C8.74942 23.9275 8.81139 23.7779 8.92171 23.6676C9.03202 23.5573 9.18164 23.4953 9.33765 23.4953H14.3271C14.4831 23.4953 14.6327 23.5573 14.743 23.6676C14.8533 23.7779 14.9153 23.9275 14.9153 24.0835C14.9153 24.2395 14.8533 24.3892 14.743 24.4995C14.6327 24.6098 14.4831 24.6718 14.3271 24.6718H9.33765C9.18164 24.6718 9.03202 24.6098 8.92171 24.4995C8.81139 24.3892 8.74942 24.2395 8.74942 24.0835ZM7.02353 24.3059C6.99551 24.2345 6.9796 24.159 6.97647 24.0824C6.97857 23.9661 7.01475 23.8529 7.08053 23.757C7.14632 23.6611 7.23881 23.5866 7.34654 23.5428C7.45427 23.4989 7.5725 23.4877 7.68657 23.5104C7.80064 23.5331 7.90553 23.5888 7.98824 23.6706C8.09476 23.7811 8.15387 23.9289 8.15295 24.0824C8.15181 24.2389 8.0932 24.3897 7.98824 24.5059C7.93048 24.5586 7.86236 24.5986 7.78824 24.6235C7.71863 24.6571 7.64193 24.6732 7.56471 24.6706C7.41122 24.6715 7.26345 24.6124 7.15294 24.5059C7.09912 24.4468 7.0554 24.3792 7.02353 24.3059ZM6.98 7.56824C6.98 7.41223 7.04198 7.26261 7.15229 7.15229C7.26261 7.04198 7.41223 6.98 7.56824 6.98H15.0588C15.2148 6.98 15.3645 7.04198 15.4748 7.15229C15.5851 7.26261 15.6471 7.41223 15.6471 7.56824C15.6471 7.72425 15.5851 7.87387 15.4748 7.98418C15.3645 8.0945 15.2148 8.15647 15.0588 8.15647H7.56471C7.40931 8.15554 7.2606 8.09316 7.15104 7.98294C7.04149 7.87273 6.98 7.72364 6.98 7.56824ZM6.98 9.92118C6.98 9.76517 7.04198 9.61555 7.15229 9.50523C7.26261 9.39492 7.41223 9.33294 7.56824 9.33294H10.3529C10.509 9.33294 10.6586 9.39492 10.7689 9.50523C10.8792 9.61555 10.9412 9.76517 10.9412 9.92118C10.9412 10.0772 10.8792 10.2268 10.7689 10.3371C10.6586 10.4474 10.509 10.5094 10.3529 10.5094H7.56471C7.40931 10.5085 7.2606 10.4461 7.15104 10.3359C7.04149 10.2257 6.98 10.0766 6.98 9.92118ZM13.2624 30L16.0082 26.5177C16.0561 26.4569 16.1154 26.4062 16.1828 26.3684C16.2502 26.3306 16.3244 26.3065 16.4011 26.2974C16.4779 26.2882 16.5557 26.2943 16.6301 26.3153C16.7045 26.3362 16.774 26.3716 16.8347 26.4194C16.8954 26.4672 16.9461 26.5265 16.9839 26.594C17.0217 26.6614 17.0459 26.7356 17.055 26.8123C17.0641 26.889 17.058 26.9668 17.0371 27.0412C17.0162 27.1156 16.9808 27.1852 16.9329 27.2459L13.7741 31.2459C13.7225 31.3115 13.6575 31.3654 13.5835 31.4039C13.5094 31.4425 13.428 31.4649 13.3447 31.4695C13.2613 31.4742 13.1779 31.4611 13.1001 31.4311C13.0222 31.401 12.9516 31.3547 12.8929 31.2953L10.7871 29.1765L8.03647 32.7765C7.94162 32.9005 7.80138 32.9818 7.64661 33.0024C7.49184 33.023 7.33521 32.9813 7.21118 32.8865C7.08715 32.7916 7.00589 32.6514 6.98526 32.4966C6.96463 32.3418 7.00633 32.1852 7.10118 32.0612L10.2635 27.9247C10.314 27.8569 10.3785 27.8009 10.4526 27.7603C10.5267 27.7197 10.6087 27.6955 10.6929 27.6894C10.7769 27.6857 10.8607 27.6992 10.9392 27.7291C11.0178 27.7591 11.0893 27.8048 11.1494 27.8635L13.2624 30ZM35.9224 1.13647H4.07765C3.2976 1.13647 2.5495 1.44635 1.99792 1.99792C1.44635 2.5495 1.13647 3.2976 1.13647 4.07765V35.9224C1.13647 36.7024 1.44635 37.4505 1.99792 38.0021C2.5495 38.5537 3.2976 38.8635 4.07765 38.8635H35.9224C36.7024 38.8635 37.4505 38.5537 38.0021 38.0021C38.5537 37.4505 38.8635 36.7024 38.8635 35.9224V4.07765C38.8635 3.2976 38.5537 2.5495 38.0021 1.99792C37.4505 1.44635 36.7024 1.13647 35.9224 1.13647ZM37.6871 35.9224C37.6871 36.3904 37.5011 36.8392 37.1702 37.1702C36.8392 37.5011 36.3904 37.6871 35.9224 37.6871H4.07765C3.60962 37.6871 3.16076 37.5011 2.82982 37.1702C2.49887 36.8392 2.31294 36.3904 2.31294 35.9224V4.07765C2.31294 3.60962 2.49887 3.16076 2.82982 2.82982C3.16076 2.49887 3.60962 2.31294 4.07765 2.31294H35.9224C36.3904 2.31294 36.8392 2.49887 37.1702 2.82982C37.5011 3.16076 37.6871 3.60962 37.6871 4.07765V35.9224Z"
          fill="#2A4D48"
          stroke="#2A4D48"
          stroke-width="0.2"
        />
      </svg>
    ),
    title: "Reports",
    description: "Access reports and analytics",
    tab: "metabase",
  },
];

function FeatureCard({
  icon,
  title,
  description,
  onClick,
}: {
  icon: React.ReactNode;
  title: string;
  description: string;
  onClick?: () => void;
}) {
  return (
    <div
      className="bg-white rounded-lg border border-gray-200 flex flex-col items-center shadow-sm p-6 flex-shrink-0 cursor-pointer transition hover:shadow-md"
      style={{ width: "13.2rem", minWidth: "11rem", height: "14rem" }}
      onClick={onClick}
    >
      <div
        className="flex items-center justify-center mb-4"
        style={{
          width: "5rem",
          height: "5rem",
          flexShrink: 0,
          aspectRatio: "1/1",
          background: "#F7F7F7",
          borderRadius: "50%",
        }}
      >
        {icon}
      </div>
      <div className="font-semibold text-[#2A4D48] mb-1 text-center">
        {title}
      </div>
      <div className="text-gray-500 text-sm text-center">{description}</div>
    </div>
  );
}

export default function LandingPage() {
  // Show only on first login (example: use localStorage)
  const [showLandingPage, setShowLandingPage] = useState(true);
  const [loading, setLoading] = useState(true); // <-- Add loading state
  const navigate = useNavigate();
  let isMounted = true;
  const handleCardClick = (tabName: string) => {
    navigate("/crm", { state: { activeTab: tabName } });
  };
  const [userEmail, setUserEmail] = useState("");
//   const location = useLocation()
  useEffect(() => {
    // Check if landing page was already shown
    // if (!showLandingPage) return null;
    const checkAuth = async () => {
      if (!isMounted) return;

      console.log("Checking auth status");
      const {
        data: { session },
      } = await supabase.auth.getSession();
      if (!session) {
        if (isMounted) {
          toast.error("Please sign in to create enquiries");
          navigate("/auth");
        }
        return;
      }

      // Fetch user's category and country from user_roles table
      const userId = session.user.id;
      console.log("Fetching user data for userId:", userId);

      try {
        const { data: userData, error: userError } = await supabase
          .from("user_roles")
          .select("category, country,role,is_crm_enabled")
          .eq("user_id", userId)
          .single();

        if (!isMounted) return;

        if (userError) {
          console.error("Error fetching user data:", userError);
          navigate("/user-not-found")
        } else if (userData) {
          // Store user data in localStorage

          localStorage.setItem("crmEnabled", String(userData.is_crm_enabled));
          setCrmEnabledEverywhere( String(userData.is_crm_enabled));
          localStorage.setItem("userCategory", userData.category || "");
          localStorage.setItem("userCountry", userData.country || "");
          localStorage.setItem("userRole", userData.role || "");
          localStorage.setItem("userId", userId);
          localStorage.setItem("userEmail", session.user.email || "");
          localStorage.setItem("crmEnabled", String(userData.is_crm_enabled));
          console.log("Stored user data in localStorage:", {
            category: userData.category,
            country: userData.country,
            role: userData.role,
            // name:userData?.name
          });

          if (userData?.is_crm_enabled) {
            navigate("/"); // homepage
          } else {
            navigate("/crm"); // CRM dashboard
          }
          setLoading(false); // <-- Show landing page if not redirecting
          console.log(session, "session");
          setUserEmail(session?.user?.email);
        }
      } catch (error) {
        navigate("/user-not-found")
        if (!isMounted) return;
        console.error("Error in checkAuth:", error);
      }
    };
    checkAuth();
    return () => {
      isMounted = false;
    };
  }, []);

  if (loading) {
    return null; // or a spinner
  }

  return (
    <SidebarLayout>
      {(tab: string) => (
        <div className="min-h-[calc(100vh-5.2rem)] bg-[#f7f9fa] flex flex-col py-0 overflow-hidden p-0">
          <div className="flex flex-1 flex-col md:flex-row items-stretch mx-auto w-full">
            {/* Left Section */}
            <div className="flex-1 flex flex-col justify-start mt-12 px-14">
              <div className="mb-1">
                <div className="w-16 h-16 rounded-full bg-[#8EEBC7] flex items-center justify-center text-3xl font-bold text-[#294d48] mb-4 mt-4">
                  {userEmail?.charAt(0)?.toLocaleUpperCase()}
                </div>
                <h1 className="text-2xl md:text-2xl font-bold text-[#000000] pb-0 mb-0 mt-8">
                  Welcome to Mstack CRM
                </h1>
                <p className="text-[#000000] text-lg">
                  Your complete customer relationship management solution
                </p>
              </div>

              <div className="w-full max-w-[43rem]">
                <div className="h-px bg-[#EAEAEA] w-full mb-8" />
                <div className="grid grid-cols-1 sm:grid-cols-3 gap-6 overflow-hidden">
                  {CARD_DATA.map((card) => (
                    <FeatureCard
                      key={card.key}
                      icon={card.icon}
                      title={card.title}
                      description={card.description}
                      onClick={() => handleCardClick(card.tab)}
                    />
                  ))}
                </div>
              </div>
            </div>
            {/* Right Section */}
            <div className="flex-1 flex items-center mb-24  px-6 md:px-0 md:mt-0">
              <img
                src={LandingPageIcon}
                alt="CRM Illustration"
                className="max-w-full h-auto"
                style={{ height: "518px", width: "674px" }}
              />
            </div>
          </div>
        </div>
      )}
    </SidebarLayout>
  );
}
