import { clsx, type ClassValue } from "clsx"
import { twMerge } from "tailwind-merge"

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs))
}

export function formatDate(dateString: string | null | undefined) {
  if (!dateString) return "N/A";

  const date = new Date(dateString);
  return date.toLocaleDateString("en-GB", {
    day: "2-digit",
    month: "2-digit",
    year: "numeric",
  });
}

export function setCrmEnabledEverywhere(value: string) {
  localStorage.setItem("crmEnabled", value);
  // Optionally, you can dispatch a custom event if you want to notify other components
  window.dispatchEvent(new Event("crmEnabledChanged"));
}
